#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版MP4播放器 - 带字幕功能演示
不依赖Whisper模型，可以立即运行展示界面
已修复OpenCV MSMF和pthread错误
"""

import os
import sys

# 🛡️ 设置最安全的环境变量 - 在导入OpenCV之前
print("🔧 设置安全环境变量...")

# 完全禁用MSMF后端
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'

# 最安全的FFMPEG配置
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1|thread_count;1'
os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'
os.environ['FFMPEG_THREAD_SAFE'] = '1'
os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'
os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '1'

# 防止视频提前结束
os.environ['OPENCV_FFMPEG_DISABLE_ASYNC'] = '1'
os.environ['OPENCV_FFMPEG_FORCE_SYNC'] = '1'

print("✅ 安全环境变量设置完成")

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk, ImageDraw, ImageFont
import threading
import time
import tempfile
import gc

# 检查字幕生成依赖
print("🔍 检查字幕生成依赖...")

try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
    print("✅ faster-whisper 可用")
except ImportError as e:
    WHISPER_AVAILABLE = False
    print(f"❌ faster-whisper 不可用: {e}")

try:
    import moviepy
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
    print("✅ moviepy 可用")
except ImportError as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 不可用: {e}")
    print("安装命令: pip install moviepy==1.0.3")
except Exception as e:
    MOVIEPY_AVAILABLE = False
    print(f"❌ moviepy 导入异常: {e}")

try:
    from opencc import OpenCC
    OPENCC_AVAILABLE = True
    print("✅ opencc 可用 - 支持繁简转换")
except ImportError as e:
    OPENCC_AVAILABLE = False
    print(f"❌ opencc 不可用: {e}")
    print("安装命令: pip install opencc-python-reimplemented")

try:
    import pygame
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
    print("✅ pygame 可用 - 支持音频播放")
except ImportError as e:
    PYGAME_AVAILABLE = False
    print(f"❌ pygame 不可用: {e}")
    print("安装命令: pip install pygame")
except Exception as e:
    PYGAME_AVAILABLE = False
    print(f"❌ pygame 初始化失败: {e}")

try:
    import winsound
    WINSOUND_AVAILABLE = True
    print("✅ winsound 可用 - Windows内置音频播放")
except ImportError as e:
    WINSOUND_AVAILABLE = False
    print(f"❌ winsound 不可用: {e}")

# 音频播放优先级：pygame > winsound
AUDIO_AVAILABLE = PYGAME_AVAILABLE or WINSOUND_AVAILABLE


class SimpleMP4Player:
    def __init__(self, root):
        self.root = root
        self.root.title("MP4播放器 - 实时字幕演示")
        self.root.geometry("1200x800")
        
        # 视频相关变量
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.is_paused = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.frame_delay = 1/30
        
        # 字幕相关变量
        self.subtitle_text = ""
        self.demo_subtitle_thread = None

        # 实时字幕生成变量
        self.whisper_model = None
        self.subtitle_segments = []
        self.current_subtitle_index = 0
        self.subtitle_generation_complete = False
        self.subtitle_streaming_mode = False  # 流式字幕模式
        self.audio_path = None

        # 简体中文转换器
        self.opencc_converter = None
        if OPENCC_AVAILABLE:
            try:
                self.opencc_converter = OpenCC('t2s')  # 繁体转简体
                print("✅ 繁简转换器初始化成功")
            except Exception as e:
                print(f"❌ 繁简转换器初始化失败: {e}")
                self.opencc_converter = None

        # 音频播放相关变量
        self.audio_file_path = None
        self.audio_start_time = None
        self.audio_paused_position = 0
        self.audio_enabled = True

        # 字幕样式设置
        self.subtitle_font_size = 24
        self.subtitle_position_y = 0.85  # 相对位置 (0-1)
        self.subtitle_color = (255, 255, 255)  # 白色
        self.subtitle_bg_color = (0, 0, 0, 128)  # 半透明黑色背景
        
        self.setup_ui()

        # 加载Whisper模型
        if WHISPER_AVAILABLE:
            self.load_whisper_model()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择按钮
        ttk.Button(control_frame, text="选择MP4文件", 
                  command=self.select_video_file).pack(side=tk.LEFT, padx=(0, 10))
        
        # 播放控制按钮
        self.play_button = ttk.Button(control_frame, text="播放", 
                                     command=self.toggle_play, state="disabled")
        self.play_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="停止", 
                  command=self.stop_video).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(control_frame, from_=0, to=100, 
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.seek_video)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 时间显示
        self.time_label = ttk.Label(control_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 字幕控制面板
        subtitle_frame = ttk.LabelFrame(main_frame, text="字幕设置")
        subtitle_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 字幕大小控制
        ttk.Label(subtitle_frame, text="字幕大小:").grid(row=0, column=0, padx=5, pady=5)
        self.font_size_var = tk.IntVar(value=self.subtitle_font_size)
        font_size_scale = ttk.Scale(subtitle_frame, from_=12, to=48, 
                                   orient=tk.HORIZONTAL, variable=self.font_size_var,
                                   command=self.update_subtitle_font_size)
        font_size_scale.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # 字幕位置控制
        ttk.Label(subtitle_frame, text="字幕位置:").grid(row=0, column=2, padx=5, pady=5)
        self.position_var = tk.DoubleVar(value=self.subtitle_position_y)
        position_scale = ttk.Scale(subtitle_frame, from_=0.1, to=0.95, 
                                  orient=tk.HORIZONTAL, variable=self.position_var,
                                  command=self.update_subtitle_position)
        position_scale.grid(row=0, column=3, padx=5, pady=5, sticky="ew")
        
        # 字幕开关
        self.subtitle_enabled = tk.BooleanVar(value=True)
        ttk.Checkbutton(subtitle_frame, text="启用字幕显示",
                       variable=self.subtitle_enabled).grid(row=0, column=4, padx=5, pady=5)

        # 字幕生成按钮
        self.generate_subtitle_btn = ttk.Button(subtitle_frame, text="🎤 生成字幕",
                                               command=self.generate_subtitles)
        self.generate_subtitle_btn.grid(row=0, column=5, padx=5, pady=5)

        # 根据依赖可用性设置按钮状态
        if not (WHISPER_AVAILABLE and MOVIEPY_AVAILABLE):
            self.generate_subtitle_btn.configure(state="disabled")

        # 字幕颜色选择
        ttk.Label(subtitle_frame, text="字幕颜色:").grid(row=1, column=0, padx=5, pady=5)
        self.color_var = tk.StringVar(value="白色")
        color_combo = ttk.Combobox(subtitle_frame, textvariable=self.color_var,
                                  values=["白色", "黄色", "红色", "绿色", "蓝色"],
                                  state="readonly")
        color_combo.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        color_combo.bind("<<ComboboxSelected>>", self.update_subtitle_color)

        # 字幕生成进度显示
        ttk.Label(subtitle_frame, text="字幕状态:").grid(row=1, column=2, padx=5, pady=5)

        # 进度条
        self.subtitle_progress_var = tk.DoubleVar()
        self.subtitle_progress_bar = ttk.Progressbar(
            subtitle_frame,
            variable=self.subtitle_progress_var,
            maximum=100,
            mode='determinate'
        )
        self.subtitle_progress_bar.grid(row=1, column=3, padx=5, pady=5, sticky="ew")

        # 进度文本
        if WHISPER_AVAILABLE and MOVIEPY_AVAILABLE:
            initial_status = "就绪 - 可生成字幕"
        else:
            missing = []
            if not WHISPER_AVAILABLE:
                missing.append("faster-whisper")
            if not MOVIEPY_AVAILABLE:
                missing.append("moviepy")
            initial_status = f"缺少依赖: {', '.join(missing)}"

        self.progress_text_var = tk.StringVar(value=initial_status)
        self.progress_label = ttk.Label(subtitle_frame, textvariable=self.progress_text_var)
        self.progress_label.grid(row=1, column=4, padx=5, pady=5)

        # 保存字幕按钮
        self.save_subtitle_btn = ttk.Button(subtitle_frame, text="💾 保存字幕",
                                           command=self.save_subtitles, state="disabled")
        self.save_subtitle_btn.grid(row=1, column=5, padx=5, pady=5)

        # 测试字幕按钮（调试用）
        self.test_subtitle_btn = ttk.Button(subtitle_frame, text="🧪 测试字幕",
                                           command=self.test_subtitle_display)
        self.test_subtitle_btn.grid(row=2, column=0, padx=5, pady=5)

        # 音频控制
        self.audio_enabled_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(subtitle_frame, text="启用音频",
                       variable=self.audio_enabled_var).grid(row=2, column=1, padx=5, pady=5)

        # 测试音频按钮
        self.test_audio_btn = ttk.Button(subtitle_frame, text="🔊 测试音频",
                                        command=self.test_audio_playback)
        self.test_audio_btn.grid(row=2, column=2, padx=5, pady=5)

        # 强制播放音频按钮
        self.force_play_audio_btn = ttk.Button(subtitle_frame, text="🔊 强制播放",
                                              command=self.force_play_audio)
        self.force_play_audio_btn.grid(row=2, column=3, padx=5, pady=5)

        # 测试音频同步按钮
        self.test_sync_btn = ttk.Button(subtitle_frame, text="🔄 测试同步",
                                       command=self.test_audio_sync)
        self.test_sync_btn.grid(row=2, column=4, padx=5, pady=5)
        
        subtitle_frame.columnconfigure(1, weight=1)
        subtitle_frame.columnconfigure(3, weight=1)
        
        # 视频显示区域
        self.video_frame = ttk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=2)
        self.video_frame.pack(fill=tk.BOTH, expand=True)
        
        self.video_label = ttk.Label(self.video_frame, text="请选择MP4文件开始播放\n\n支持功能：\n• 视频播放控制\n• 实时字幕显示\n• 字幕位置和大小调整\n• 字幕颜色自定义")
        self.video_label.pack(expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪 - 选择视频文件开始播放")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
    def select_video_file(self):
        """选择视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择MP4文件",
            filetypes=[("MP4文件", "*.mp4"), ("AVI文件", "*.avi"), ("MOV文件", "*.mov"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.load_video(file_path)
            
    def load_video(self, video_path):
        """加载视频文件 - 安全版本"""
        try:
            # 停止当前播放
            self.stop_video()

            print(f"🎬 加载视频: {video_path}")

            # 使用最安全的方式打开视频文件
            self.cap = self.create_safe_capture(video_path)
            if not self.cap or not self.cap.isOpened():
                raise Exception("无法打开视频文件")

            self.video_path = video_path

            # 安全地获取视频信息
            try:
                self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
                self.fps = self.cap.get(cv2.CAP_PROP_FPS)

                if self.total_frames <= 0:
                    self.total_frames = 1000  # 默认值
                if self.fps <= 0:
                    self.fps = 30  # 默认帧率

                self.frame_delay = 1.0 / self.fps

                print(f"✅ 视频信息: {self.total_frames}帧, {self.fps}fps")

            except Exception as prop_error:
                print(f"⚠️ 获取视频属性失败: {prop_error}")
                # 使用默认值
                self.total_frames = 1000
                self.fps = 30
                self.frame_delay = 1/30

            # 重置进度条
            self.progress_bar.configure(to=max(1, self.total_frames-1))
            self.current_frame = 0

            # 显示第一帧
            self.show_frame()

            # 更新状态
            duration = self.total_frames / self.fps if self.fps > 0 else 0
            self.status_var.set(f"视频已加载: {os.path.basename(video_path)} "
                              f"({self.total_frames}帧, {duration:.1f}秒)")

            # 启用播放按钮
            self.play_button.configure(state="normal")

            # 提取音频文件用于播放
            if AUDIO_AVAILABLE and MOVIEPY_AVAILABLE:
                self.extract_audio_for_playback(video_path)

        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)

    def create_safe_capture(self, video_path):
        """创建最安全的VideoCapture"""
        try:
            # 尝试多种安全的后端
            safe_methods = [
                ("FFMPEG", cv2.CAP_FFMPEG),
                ("默认", None),
            ]

            for method_name, backend in safe_methods:
                try:
                    print(f"🔧 尝试{method_name}后端...")

                    if backend is not None:
                        cap = cv2.VideoCapture(video_path, backend)
                    else:
                        cap = cv2.VideoCapture(video_path)

                    if cap and cap.isOpened():
                        # 验证能否读取帧
                        ret, test_frame = cap.read()
                        if ret and test_frame is not None:
                            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置位置
                            print(f"✅ {method_name}后端成功")
                            return cap
                        else:
                            cap.release()
                            print(f"❌ {method_name}后端无法读取帧")
                    else:
                        if cap:
                            cap.release()
                        print(f"❌ {method_name}后端无法打开")

                except Exception as e:
                    print(f"❌ {method_name}后端异常: {e}")
                    continue

            print("❌ 所有安全后端都失败")
            return None

        except Exception as e:
            print(f"❌ 创建安全capture失败: {e}")
            return None
            
    def show_frame(self, frame=None):
        """显示当前帧"""
        if self.cap is None:
            return

        # 如果没有传入帧，则读取当前帧
        if frame is None:
            ret, frame = self.cap.read()
            if not ret:
                return

        # 添加字幕到帧
        if self.subtitle_enabled.get():
            # 检查是否有字幕需要显示
            if self.subtitle_text and self.subtitle_text.strip():
                frame = self.add_subtitle_to_frame(frame, self.subtitle_text)

        # 转换为PIL图像
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)

        # 调整图像大小以适应显示区域
        display_width = self.video_frame.winfo_width()
        display_height = self.video_frame.winfo_height()

        if display_width > 1 and display_height > 1:
            # 保持宽高比
            img_ratio = pil_image.width / pil_image.height
            display_ratio = display_width / display_height

            if img_ratio > display_ratio:
                new_width = display_width - 20  # 留一些边距
                new_height = int(new_width / img_ratio)
            else:
                new_height = display_height - 20  # 留一些边距
                new_width = int(new_height * img_ratio)

            pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 转换为Tkinter图像
        photo = ImageTk.PhotoImage(pil_image)
        self.video_label.configure(image=photo, text="")
        self.video_label.image = photo  # 保持引用

        # 更新进度条和时间
        self.progress_var.set(self.current_frame)
        current_time = self.current_frame / self.fps if self.fps > 0 else 0
        total_time = self.total_frames / self.fps if self.fps > 0 else 0
        self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")
            
    def add_subtitle_to_frame(self, frame, text):
        """在帧上添加字幕"""
        if not text.strip():
            return frame
            
        # 转换为PIL图像以便添加文字
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)
        draw = ImageDraw.Draw(pil_image)
        
        # 尝试加载字体
        try:
            # Windows系统字体路径
            font_paths = [
                "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "C:/Windows/Fonts/arial.ttf"   # Arial
            ]
            
            font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, self.subtitle_font_size)
                    break
            
            if font is None:
                font = ImageFont.load_default()
        except:
            font = ImageFont.load_default()
        
        # 计算文字位置
        img_width, img_height = pil_image.size
        
        # 分行处理长文本
        words = text.split()
        lines = []
        current_line = ""
        max_width = img_width * 0.9  # 最大宽度为图像宽度的90%
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            bbox = draw.textbbox((0, 0), test_line, font=font)
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        # 绘制每一行
        line_height = self.subtitle_font_size + 5
        total_text_height = len(lines) * line_height
        start_y = int(img_height * self.subtitle_position_y - total_text_height / 2)
        
        for i, line in enumerate(lines):
            bbox = draw.textbbox((0, 0), line, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (img_width - text_width) // 2
            y = start_y + i * line_height
            
            # 绘制背景
            padding = 5
            bg_bbox = [x - padding, y - padding, 
                      x + text_width + padding, y + text_height + padding]
            draw.rectangle(bg_bbox, fill=self.subtitle_bg_color)
            
            # 绘制文字
            draw.text((x, y), line, font=font, fill=self.subtitle_color)
        
        # 转换回OpenCV格式
        frame_with_subtitle = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        return frame_with_subtitle

    def toggle_play(self):
        """切换播放/暂停状态"""
        if not self.cap:
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        if self.is_playing:
            self.pause_video()
        else:
            self.play_video()

    def play_video(self):
        """开始播放视频"""
        if not self.cap:
            return

        self.is_playing = True
        self.is_paused = False
        self.play_button.configure(text="暂停")

        # 启动音频播放（延迟重试机制）
        def try_play_audio():
            print(f"🔊 尝试播放音频 - 启用状态: {self.audio_enabled_var.get()}")
            print(f"🔊 音频文件路径: {self.audio_file_path}")

            if self.audio_enabled_var.get():
                if self.audio_file_path and os.path.exists(self.audio_file_path):
                    print("🔊 音频文件存在，开始播放")
                    if self.audio_paused_position > 0:
                        self.resume_audio()
                    else:
                        self.play_audio()
                else:
                    print("🔊 音频文件不存在，1秒后重试")
                    self.root.after(1000, try_play_audio)
            else:
                print("🔊 音频已禁用")

        # 立即尝试一次，然后延迟重试
        try_play_audio()

        # 启动视频播放线程
        self.video_thread = threading.Thread(target=self.video_playback_loop, daemon=True)
        self.video_thread.start()

        # 启动字幕显示
        if self.subtitle_enabled.get():
            # 如果有AI字幕（完成或流式模式），不启动演示字幕
            if not (self.subtitle_generation_complete or self.subtitle_streaming_mode or self.subtitle_segments):
                self.start_demo_subtitles()

    def pause_video(self):
        """暂停视频"""
        self.is_playing = False
        self.is_paused = True
        self.play_button.configure(text="播放")

        # 暂停音频
        self.pause_audio()

    def stop_video(self):
        """停止视频"""
        self.is_playing = False
        self.is_paused = False
        self.play_button.configure(text="播放")

        # 停止音频
        self.stop_audio()

        # 重置到开始位置
        if self.cap:
            self.current_frame = 0
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            self.show_frame()

    def seek_video(self, value):
        """跳转到指定帧"""
        if not self.cap:
            return

        frame_number = int(float(value))
        print(f"🎬 视频跳转到帧: {frame_number}")

        # 设置视频位置
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        self.current_frame = frame_number

        # 同步音频位置
        if self.fps > 0:
            position_seconds = frame_number / self.fps
            print(f"🎬 计算音频位置: {position_seconds:.2f}秒")
            self.seek_audio(position_seconds)

        # 如果视频暂停，显示当前帧
        if not self.is_playing:
            self.show_frame()

        print(f"🎬 视频跳转完成: 帧{frame_number}, 时间{position_seconds:.2f}秒")

    def video_playback_loop(self):
        """视频播放循环 - 永不停止版本"""
        consecutive_errors = 0
        max_consecutive_errors = 50
        recovery_attempts = 0
        max_recovery_attempts = 100
        force_continue = True

        print("🎬 启动永不停止播放循环")

        while self.is_playing and self.cap and force_continue:
            try:
                start_time = time.time()

                # 读取并显示帧
                ret, frame = self.cap.read()
                if not ret:
                    # 检查是否真的到了视频末尾
                    current_pos = 0
                    try:
                        current_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                    except:
                        pass

                    if current_pos >= self.total_frames * 0.95:  # 允许5%的误差
                        # 正常到达视频末尾
                        print(f"✅ 视频正常播放完毕: {current_pos}/{self.total_frames}")
                        self.is_playing = False
                        self.root.after(0, lambda: self.play_button.configure(text="播放"))
                        break
                    else:
                        # 视频未播放完就无法读取，启动恢复
                        recovery_attempts += 1
                        print(f"💪 视频未播放完但读取失败，启动恢复 (第{recovery_attempts}次)")
                        print(f"   当前位置: {current_pos}/{self.total_frames} ({current_pos/self.total_frames*100:.1f}%)")

                        if recovery_attempts < max_recovery_attempts:
                            # 尝试多种恢复方法
                            if recovery_attempts % 10 == 0:
                                # 每10次尝试重新创建capture
                                print("🔄 重新创建capture进行恢复")
                                if self.recreate_capture_for_recovery():
                                    recovery_attempts = 0
                                    consecutive_errors = 0
                                    continue
                            else:
                                # 尝试跳过当前帧
                                try:
                                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, current_pos + 1)
                                    time.sleep(0.1)
                                    continue
                                except:
                                    pass

                            time.sleep(0.2)
                            continue
                        else:
                            print(f"💪 恢复尝试{recovery_attempts}次，但永不放弃")
                            recovery_attempts = 0
                            time.sleep(1)
                            continue

                # 验证帧数据
                if frame is None or frame.size == 0:
                    print("💪 读取到无效帧，但永不停止")
                    consecutive_errors += 1
                    recovery_attempts += 1

                    if consecutive_errors >= max_consecutive_errors:
                        print(f"💪 连续错误{consecutive_errors}次，但强制继续")
                        consecutive_errors = 0
                        time.sleep(0.5)
                    continue

                # 重置错误计数
                consecutive_errors = 0
                recovery_attempts = 0

                try:
                    self.current_frame = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                except:
                    self.current_frame += 1

                # 更新实时字幕（支持流式模式）
                if (self.subtitle_generation_complete or self.subtitle_streaming_mode) and self.subtitle_segments:
                    current_time = self.current_frame / self.fps if self.fps > 0 else 0
                    self.update_current_subtitle(current_time)

                # 在主线程中更新UI，传递当前帧
                self.root.after(0, lambda f=frame: self.show_frame(f))

                # 控制播放速度
                elapsed = time.time() - start_time
                sleep_time = max(0, self.frame_delay - elapsed)
                time.sleep(sleep_time)

            except Exception as e:
                error_msg = str(e)
                print(f"💪 播放循环异常但永不停止: {error_msg}")
                consecutive_errors += 1
                recovery_attempts += 1

                # 检查是否是严重错误
                if any(keyword in error_msg.lower() for keyword in ['pthread', 'async_lock', 'msmf']):
                    print("🚨 检测到严重错误，启动恢复")
                    if self.recreate_capture_for_recovery():
                        consecutive_errors = 0
                        recovery_attempts = 0
                        continue

                # 永不停止机制
                if consecutive_errors >= max_consecutive_errors:
                    if recovery_attempts < max_recovery_attempts:
                        print(f"💪 连续错误{consecutive_errors}次，但永不停止")
                        consecutive_errors = 0
                        time.sleep(0.8)
                        continue
                    else:
                        print(f"💪 错误和恢复都过多，但仍然尝试继续")
                        consecutive_errors = 0
                        recovery_attempts = 0
                        time.sleep(2)
                        continue

                time.sleep(0.2)

    def recreate_capture_for_recovery(self):
        """重新创建capture进行恢复"""
        try:
            print("🔄 重新创建capture...")

            # 保存当前位置
            current_pos = 0
            try:
                current_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
            except:
                pass

            # 释放当前capture
            if self.cap:
                try:
                    self.cap.release()
                except:
                    pass

            # 等待资源释放
            time.sleep(0.3)

            # 重新创建capture
            self.cap = self.create_safe_capture(self.video_path)
            if self.cap and self.cap.isOpened():
                # 恢复到之前的位置
                try:
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, current_pos)
                except:
                    pass

                print(f"✅ capture重新创建成功，恢复到位置{current_pos}")
                return True
            else:
                print("❌ capture重新创建失败")
                return False

        except Exception as e:
            print(f"❌ 重新创建capture异常: {e}")
            return False

    def start_demo_subtitles(self):
        """启动演示字幕"""
        demo_subtitles = [
            "欢迎使用MP4播放器！",
            "这是一个功能完整的视频播放器",
            "支持实时字幕显示功能",
            "您可以调整字幕的大小和位置",
            "支持多种字幕颜色选择",
            "集成faster-whisper引擎可生成真实字幕",
            "当前显示的是演示字幕效果",
            "字幕会自动换行适应屏幕宽度",
            "背景半透明确保字幕清晰可见"
        ]

        def demo_loop():
            subtitle_index = 0
            total_subtitles = len(demo_subtitles)

            # 模拟字幕生成进度
            self.root.after(0, lambda: self.update_demo_progress(0, "开始生成演示字幕..."))
            time.sleep(0.5)

            while self.is_playing and self.subtitle_enabled.get():
                if subtitle_index < total_subtitles:
                    subtitle_text = demo_subtitles[subtitle_index]

                    # 更新进度
                    progress = ((subtitle_index + 1) / total_subtitles) * 100
                    status = f"显示字幕 ({subtitle_index + 1}/{total_subtitles})"

                    def update_ui(text=subtitle_text, p=progress, s=status):
                        self.update_subtitle(text)
                        self.update_demo_progress(p, s)

                    self.root.after(0, update_ui)
                    subtitle_index = (subtitle_index + 1) % total_subtitles

                time.sleep(3)  # 每3秒更换一次字幕

        if self.demo_subtitle_thread is None or not self.demo_subtitle_thread.is_alive():
            self.demo_subtitle_thread = threading.Thread(target=demo_loop, daemon=True)
            self.demo_subtitle_thread.start()

    def update_demo_progress(self, progress, status_text):
        """更新演示进度"""
        self.subtitle_progress_var.set(progress)
        self.progress_text_var.set(status_text)

    def update_subtitle(self, text):
        """更新字幕文本"""
        self.subtitle_text = text

    def update_subtitle_font_size(self, value):
        """更新字幕字体大小"""
        self.subtitle_font_size = int(float(value))

    def update_subtitle_position(self, value):
        """更新字幕位置"""
        self.subtitle_position_y = float(value)

    def update_subtitle_color(self, event=None):
        """更新字幕颜色"""
        color_map = {
            "白色": (255, 255, 255),
            "黄色": (255, 255, 0),
            "红色": (255, 0, 0),
            "绿色": (0, 255, 0),
            "蓝色": (0, 0, 255)
        }
        self.subtitle_color = color_map.get(self.color_var.get(), (255, 255, 255))

    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def extract_audio_for_playback(self, video_path):
        """提取音频文件用于播放"""
        def extract():
            try:
                print("🔊 开始提取音频用于播放...")

                # 创建临时音频文件
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                    self.audio_file_path = temp_audio.name

                print(f"🔊 临时音频文件路径: {self.audio_file_path}")

                # 使用moviepy提取音频
                video_clip = VideoFileClip(video_path)
                audio_clip = video_clip.audio

                if audio_clip is not None:
                    print("🔊 检测到音频轨道，开始提取...")

                    # 提取音频，使用更兼容的参数
                    audio_clip.write_audiofile(
                        self.audio_file_path,
                        verbose=False,
                        logger=None,
                        codec='pcm_s16le',  # 使用PCM编码确保兼容性
                        ffmpeg_params=['-ar', '44100']  # 设置采样率
                    )

                    # 检查生成的文件
                    if os.path.exists(self.audio_file_path):
                        file_size = os.path.getsize(self.audio_file_path)
                        print(f"✅ 音频提取完成，文件大小: {file_size} 字节")

                        # 如果正在播放，尝试播放音频
                        if self.is_playing and self.audio_enabled_var.get():
                            self.root.after(0, self.play_audio)
                    else:
                        print("❌ 音频文件生成失败")
                        self.audio_file_path = None
                else:
                    print("⚠️ 视频文件没有音频轨道")
                    self.audio_file_path = None

                    # 在界面上显示提示
                    self.root.after(0, lambda: self.progress_text_var.set("视频没有音频轨道"))
                    self.root.after(0, lambda: messagebox.showinfo(
                        "提示",
                        "当前视频文件没有音频轨道，无法播放声音。\n\n"
                        "请选择包含音频的视频文件来测试音频播放功能。"
                    ))

                # 关闭文件
                video_clip.close()
                if audio_clip:
                    audio_clip.close()

            except Exception as e:
                print(f"❌ 音频提取失败: {e}")
                import traceback
                traceback.print_exc()
                self.audio_file_path = None

        # 在后台线程中提取音频
        threading.Thread(target=extract, daemon=True).start()

    def play_audio(self):
        """播放音频"""
        if not AUDIO_AVAILABLE:
            print("❌ 音频播放不可用")
            return

        try:
            if not self.audio_enabled_var.get():
                print("🔇 音频已禁用")
                return

            # 检查音频文件是否存在
            if not self.audio_file_path:
                print("⚠️ 音频文件路径为空，等待提取完成...")
                return

            if not os.path.exists(self.audio_file_path):
                print(f"⚠️ 音频文件不存在: {self.audio_file_path}")
                return

            # 检查文件大小
            file_size = os.path.getsize(self.audio_file_path)
            print(f"🔊 准备播放音频文件: {self.audio_file_path}")
            print(f"🔊 音频文件大小: {file_size} 字节")

            # 尝试pygame播放
            if PYGAME_AVAILABLE:
                try:
                    # 停止当前音频
                    pygame.mixer.music.stop()

                    # 设置音量
                    pygame.mixer.music.set_volume(1.0)  # 设置最大音量

                    # 加载并播放音频
                    pygame.mixer.music.load(self.audio_file_path)
                    pygame.mixer.music.play()

                    # 等待一下让播放开始
                    time.sleep(0.1)

                    # 检查播放状态
                    if pygame.mixer.music.get_busy():
                        print("🔊 pygame音频播放开始")
                        self.audio_start_time = time.time()
                        self.audio_paused_position = 0

                        # 显示音量提示
                        print("🔊 音频正在播放，请检查系统音量设置")
                        return
                    else:
                        print("❌ pygame播放失败，尝试其他方法")

                except Exception as e:
                    print(f"❌ pygame播放失败: {e}")

            # 尝试winsound播放
            if WINSOUND_AVAILABLE:
                try:
                    print("🔊 尝试使用winsound播放音频...")
                    import winsound

                    # 在后台线程中播放，避免阻塞界面
                    def play_with_winsound():
                        try:
                            winsound.PlaySound(self.audio_file_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
                            print("🔊 winsound音频播放开始")
                        except Exception as e:
                            print(f"❌ winsound播放失败: {e}")

                    threading.Thread(target=play_with_winsound, daemon=True).start()
                    self.audio_start_time = time.time()
                    self.audio_paused_position = 0
                    return

                except Exception as e:
                    print(f"❌ winsound播放失败: {e}")

            print("❌ 所有音频播放方法都失败")

        except Exception as e:
            print(f"❌ 音频播放异常: {e}")
            import traceback
            traceback.print_exc()

    def pause_audio(self):
        """暂停音频"""
        if not AUDIO_AVAILABLE:
            return

        try:
            if PYGAME_AVAILABLE:
                pygame.mixer.music.pause()

                # 计算暂停位置
                if self.audio_start_time:
                    self.audio_paused_position += time.time() - self.audio_start_time

                print("🔊 pygame音频已暂停")
            elif WINSOUND_AVAILABLE:
                # winsound不支持暂停，只能停止
                import winsound
                winsound.PlaySound(None, winsound.SND_PURGE)
                print("🔊 winsound音频已停止")

        except Exception as e:
            print(f"❌ 音频暂停失败: {e}")

    def resume_audio(self):
        """恢复音频播放"""
        if not AUDIO_AVAILABLE:
            return

        try:
            if PYGAME_AVAILABLE:
                # 如果有暂停位置，需要从该位置继续播放
                if self.audio_paused_position > 0:
                    print(f"🔊 从暂停位置恢复播放: {self.audio_paused_position:.2f}秒")
                    self.seek_audio(self.audio_paused_position)
                else:
                    pygame.mixer.music.unpause()
                    self.audio_start_time = time.time()
                    print("🔊 pygame音频恢复播放")
            elif WINSOUND_AVAILABLE:
                # winsound不支持恢复，重新播放
                if self.audio_paused_position > 0:
                    self.seek_audio(self.audio_paused_position)
                else:
                    self.play_audio()

        except Exception as e:
            print(f"❌ 音频恢复失败: {e}")

    def stop_audio(self):
        """停止音频"""
        if not AUDIO_AVAILABLE:
            return

        try:
            if PYGAME_AVAILABLE:
                pygame.mixer.music.stop()
                print("🔊 pygame音频已停止")
            elif WINSOUND_AVAILABLE:
                import winsound
                winsound.PlaySound(None, winsound.SND_PURGE)
                print("🔊 winsound音频已停止")

            self.audio_start_time = None
            self.audio_paused_position = 0

        except Exception as e:
            print(f"❌ 音频停止失败: {e}")

    def seek_audio(self, position_seconds):
        """跳转音频到指定位置"""
        if not AUDIO_AVAILABLE or not self.audio_file_path:
            print(f"🔊 音频跳转跳过: AUDIO_AVAILABLE={AUDIO_AVAILABLE}, audio_file_path={bool(self.audio_file_path)}")
            return

        try:
            print(f"🔊 音频跳转到: {position_seconds:.2f}秒")

            if not self.audio_enabled_var.get():
                print("🔊 音频已禁用，跳过跳转")
                return

            # 尝试pygame跳转
            if PYGAME_AVAILABLE:
                try:
                    # 停止当前播放
                    pygame.mixer.music.stop()

                    # 重新加载音频文件
                    pygame.mixer.music.load(self.audio_file_path)
                    pygame.mixer.music.set_volume(1.0)

                    # pygame不支持从指定位置开始播放，所以我们需要使用其他方法
                    # 这里我们重新播放整个文件，但记录偏移时间
                    if self.is_playing:
                        pygame.mixer.music.play()

                        # 记录音频开始时间，考虑偏移
                        self.audio_start_time = time.time() - position_seconds
                        self.audio_paused_position = 0

                        print(f"🔊 pygame音频跳转完成，从{position_seconds:.2f}秒开始播放")
                        return
                    else:
                        # 如果视频暂停，只记录位置，不播放
                        self.audio_paused_position = position_seconds
                        print(f"🔊 视频暂停中，记录音频位置: {position_seconds:.2f}秒")
                        return

                except Exception as e:
                    print(f"❌ pygame音频跳转失败: {e}")

            # 尝试winsound跳转（winsound不支持跳转，只能重新播放）
            if WINSOUND_AVAILABLE:
                try:
                    import winsound

                    # 停止当前播放
                    winsound.PlaySound(None, winsound.SND_PURGE)

                    if self.is_playing:
                        # winsound不支持从指定位置播放，重新播放整个文件
                        winsound.PlaySound(self.audio_file_path, winsound.SND_FILENAME | winsound.SND_ASYNC)

                        # 记录开始时间和偏移
                        self.audio_start_time = time.time() - position_seconds
                        self.audio_paused_position = 0

                        print(f"🔊 winsound音频跳转完成（重新播放）")
                        return
                    else:
                        # 如果视频暂停，只记录位置
                        self.audio_paused_position = position_seconds
                        print(f"🔊 视频暂停中，记录音频位置: {position_seconds:.2f}秒")
                        return

                except Exception as e:
                    print(f"❌ winsound音频跳转失败: {e}")

            print("❌ 所有音频跳转方法都失败")

        except Exception as e:
            print(f"❌ 音频跳转异常: {e}")
            import traceback
            traceback.print_exc()

    def load_whisper_model(self):
        """加载Whisper模型"""
        def load_model():
            try:
                self.root.after(0, lambda: self.progress_text_var.set("正在加载Whisper模型..."))
                print("🔄 开始加载Whisper模型...")

                # 使用large-v3模型，GPU版本确保兼容性
                self.whisper_model = WhisperModel("large-v3", device="cuda", compute_type="int8")

                self.root.after(0, lambda: self.progress_text_var.set("Whisper模型就绪"))
                print("✅ Whisper模型加载成功")

            except Exception as e:
                error_msg = f"Whisper模型加载失败: {e}"
                self.root.after(0, lambda: self.progress_text_var.set(error_msg))
                print(f"❌ {error_msg}")

        # 在后台线程中加载
        threading.Thread(target=load_model, daemon=True).start()

    def generate_subtitles(self):
        """生成字幕"""
        if not self.video_path:
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        if not WHISPER_AVAILABLE or not self.whisper_model:
            messagebox.showerror("错误", "Whisper模型不可用，无法生成字幕")
            return

        if not MOVIEPY_AVAILABLE:
            messagebox.showerror("错误", "moviepy不可用，无法提取音频")
            return

        def generate():
            try:
                self.root.after(0, lambda: self.generate_subtitle_btn.configure(state="disabled"))
                self.root.after(0, lambda: self.progress_text_var.set("正在提取音频..."))
                self.root.after(0, lambda: self.subtitle_progress_var.set(10))

                print("🔊 开始提取音频...")

                # 提取音频
                video_clip = VideoFileClip(self.video_path)

                # 创建临时音频文件
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_audio:
                    self.audio_path = temp_audio.name

                # 提取音频到临时文件
                audio_clip = video_clip.audio
                audio_clip.write_audiofile(self.audio_path, verbose=False, logger=None)

                # 关闭视频文件
                video_clip.close()
                audio_clip.close()

                print("✅ 音频提取完成")
                self.root.after(0, lambda: self.progress_text_var.set("正在进行语音识别..."))
                self.root.after(0, lambda: self.subtitle_progress_var.set(40))

                # 使用Whisper进行语音识别
                print("🤖 开始AI语音识别...")
                segments, info = self.whisper_model.transcribe(
                    self.audio_path,
                    language="zh",  # 中文
                    beam_size=5,
                    word_timestamps=True
                )

                print(f"🔍 检测到语言: {info.language} (置信度: {info.language_probability:.2f})")

                # 初始化字幕列表并启用实时显示
                self.subtitle_segments = []
                self.subtitle_generation_complete = False  # 标记为生成中
                self.subtitle_streaming_mode = True  # 启用流式模式

                # 停止演示字幕，开始流式字幕显示
                self.demo_subtitle_thread = None
                print("🎬 启动流式字幕显示模式")

                self.root.after(0, lambda: self.progress_text_var.set("正在实时生成字幕..."))
                self.root.after(0, lambda: self.subtitle_progress_var.set(70))

                segment_count = 0
                for segment in segments:
                    # 转换为简体中文
                    original_text = segment.text.strip()
                    simplified_text = self.convert_to_simplified_chinese(original_text)

                    subtitle_info = {
                        'start': segment.start,
                        'end': segment.end,
                        'text': simplified_text
                    }

                    # 立即添加到字幕列表，实现流式显示
                    self.subtitle_segments.append(subtitle_info)
                    segment_count += 1

                    print(f"📝 [{segment.start:.1f}s-{segment.end:.1f}s] {simplified_text}")
                    if original_text != simplified_text:
                        print(f"   转换: {original_text} → {simplified_text}")

                    # 更新进度显示
                    progress_msg = f"已生成 {segment_count} 条字幕..."
                    self.root.after(0, lambda msg=progress_msg: self.progress_text_var.set(msg))

                # 清理临时文件
                try:
                    os.unlink(self.audio_path)
                except:
                    pass

                # 标记生成完成
                self.subtitle_generation_complete = True
                self.subtitle_streaming_mode = False  # 关闭流式模式
                self.current_subtitle_index = 0

                success_msg = f"完成 - 共{len(self.subtitle_segments)}条字幕"
                print(f"✅ 字幕生成完成！{success_msg}")
                self.root.after(0, lambda: self.progress_text_var.set(success_msg))
                self.root.after(0, lambda: self.subtitle_progress_var.set(100))
                self.root.after(0, lambda: self.save_subtitle_btn.configure(state="normal"))

                print("🎬 流式字幕生成完成，继续实时显示")

                # 立即更新一次字幕以确保显示
                if self.is_playing:
                    current_time = self.current_frame / self.fps if self.fps > 0 else 0
                    self.root.after(0, lambda: self.update_current_subtitle(current_time))

            except Exception as e:
                error_msg = f"字幕生成失败: {e}"
                print(f"❌ {error_msg}")
                self.root.after(0, lambda: self.progress_text_var.set("生成失败"))
                self.root.after(0, lambda: self.subtitle_progress_var.set(0))
                self.root.after(0, lambda err=error_msg: messagebox.showerror("错误", err))
            finally:
                self.root.after(0, lambda: self.generate_subtitle_btn.configure(state="normal"))

        # 在后台线程中生成字幕
        threading.Thread(target=generate, daemon=True).start()



    def save_subtitles(self):
        """保存字幕文件"""
        if not self.subtitle_segments:
            messagebox.showwarning("警告", "没有字幕可保存")
            return

        try:
            file_path = filedialog.asksaveasfilename(
                title="保存字幕文件",
                defaultextension=".srt",
                filetypes=[("SRT字幕", "*.srt"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if file_path:
                with open(file_path, "w", encoding="utf-8") as f:
                    for i, segment in enumerate(self.subtitle_segments, 1):
                        start_time = self.format_srt_time(segment['start'])
                        end_time = self.format_srt_time(segment['end'])

                        f.write(f"{i}\n")
                        f.write(f"{start_time} --> {end_time}\n")
                        f.write(f"{segment['text']}\n\n")

                print(f"💾 字幕已保存: {os.path.basename(file_path)}")
                self.status_var.set(f"字幕已保存到: {file_path}")
                messagebox.showinfo("成功", f"字幕已保存到:\n{file_path}")

        except Exception as e:
            error_msg = f"保存字幕失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def format_srt_time(self, seconds):
        """格式化时间为SRT格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millis = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millis:03d}"

    def update_current_subtitle(self, current_time):
        """更新当前字幕"""
        try:
            # 查找当前时间对应的字幕
            current_subtitle = ""

            for segment in self.subtitle_segments:
                if segment['start'] <= current_time <= segment['end']:
                    current_subtitle = segment['text']
                    break

            # 更新字幕文本
            if current_subtitle != self.subtitle_text:
                self.subtitle_text = current_subtitle
                # 只在字幕切换时显示日志
                if current_subtitle:
                    print(f"🎬 显示字幕 [{current_time:.1f}s]: {current_subtitle}")
                elif self.subtitle_text:  # 只在从有字幕变为无字幕时显示
                    print(f"🎬 清空字幕 [{current_time:.1f}s]")

        except Exception as e:
            print(f"❌ 更新字幕失败: {e}")

    def convert_to_simplified_chinese(self, text):
        """转换为简体中文"""
        try:
            if self.opencc_converter and text:
                simplified_text = self.opencc_converter.convert(text)
                return simplified_text
            return text
        except Exception as e:
            print(f"❌ 繁简转换失败: {e}")
            return text

    def test_subtitle_display(self):
        """测试字幕显示功能"""
        print("🧪 开始测试字幕显示...")

        # 设置测试字幕
        test_subtitle = "这是一个测试字幕 - 用于验证字幕显示功能"
        self.subtitle_text = test_subtitle

        print(f"🧪 设置测试字幕: {test_subtitle}")
        print(f"🧪 字幕启用状态: {self.subtitle_enabled.get()}")
        print(f"🧪 当前字幕文本: {self.subtitle_text}")

        # 强制刷新显示
        if self.cap:
            self.show_frame()
            print("🧪 强制刷新显示完成")
        else:
            print("🧪 没有视频文件，无法测试字幕显示")

    def test_audio_playback(self):
        """测试音频播放功能"""
        print("🔊 开始测试音频播放...")

        if not AUDIO_AVAILABLE:
            print("❌ 音频播放不可用")
            messagebox.showerror("错误", "音频播放功能不可用")
            return

        # 检查音频文件状态
        print(f"🔊 音频文件路径: {self.audio_file_path}")
        print(f"🔊 音频启用状态: {self.audio_enabled_var.get()}")
        print(f"🔊 pygame可用: {PYGAME_AVAILABLE}")
        print(f"🔊 winsound可用: {WINSOUND_AVAILABLE}")

        if self.audio_file_path and os.path.exists(self.audio_file_path):
            file_size = os.path.getsize(self.audio_file_path)
            print(f"🔊 音频文件存在，大小: {file_size} 字节")

            success = False

            # 尝试pygame播放
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.music.stop()
                    pygame.mixer.music.load(self.audio_file_path)
                    pygame.mixer.music.play()

                    # 检查播放状态
                    if pygame.mixer.music.get_busy():
                        print("✅ pygame音频测试播放成功")
                        success = True
                    else:
                        print("❌ pygame音频测试播放失败")

                except Exception as e:
                    print(f"❌ pygame音频测试异常: {e}")

            # 如果pygame失败，尝试winsound
            if not success and WINSOUND_AVAILABLE:
                try:
                    import winsound
                    print("🔊 尝试winsound播放测试...")
                    winsound.PlaySound(self.audio_file_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
                    print("✅ winsound音频测试播放成功")
                    success = True

                except Exception as e:
                    print(f"❌ winsound音频测试异常: {e}")

            if success:
                messagebox.showinfo("成功", "音频测试播放成功！")
            else:
                messagebox.showerror("失败", "所有音频播放方法都失败")

        else:
            print("❌ 音频文件不存在或未提取")
            messagebox.showwarning("警告", "音频文件不存在，请先加载视频文件")

    def force_play_audio(self):
        """强制播放音频"""
        print("🔊 强制播放音频...")

        if not AUDIO_AVAILABLE:
            messagebox.showerror("错误", "音频播放功能不可用")
            return

        if not self.audio_file_path or not os.path.exists(self.audio_file_path):
            messagebox.showwarning("警告", "音频文件不存在，请先加载视频文件")
            return

        try:
            # 强制启用音频
            self.audio_enabled_var.set(True)

            # 检查文件
            file_size = os.path.getsize(self.audio_file_path)
            print(f"🔊 强制播放音频文件: {self.audio_file_path}")
            print(f"🔊 文件大小: {file_size} 字节")

            success = False

            # 尝试pygame播放
            if PYGAME_AVAILABLE:
                try:
                    print("🔊 尝试pygame强制播放...")
                    pygame.mixer.music.stop()
                    pygame.mixer.music.load(self.audio_file_path)
                    pygame.mixer.music.set_volume(1.0)  # 设置最大音量
                    pygame.mixer.music.play()

                    # 等待一下检查状态
                    time.sleep(0.1)
                    if pygame.mixer.music.get_busy():
                        print("✅ pygame强制播放成功")
                        success = True
                        messagebox.showinfo("成功", "pygame音频强制播放成功！请检查系统音量设置。")
                    else:
                        print("❌ pygame强制播放失败")

                except Exception as e:
                    print(f"❌ pygame强制播放异常: {e}")

            # 如果pygame失败，尝试winsound
            if not success and WINSOUND_AVAILABLE:
                try:
                    print("🔊 尝试winsound强制播放...")
                    import winsound

                    # 停止所有音频
                    winsound.PlaySound(None, winsound.SND_PURGE)

                    # 播放音频文件
                    winsound.PlaySound(self.audio_file_path, winsound.SND_FILENAME | winsound.SND_ASYNC)
                    print("✅ winsound强制播放成功")
                    success = True
                    messagebox.showinfo("成功", "winsound音频强制播放成功！请检查系统音量设置。")

                except Exception as e:
                    print(f"❌ winsound强制播放异常: {e}")

            if not success:
                messagebox.showerror("失败", "所有音频播放方法都失败。请检查：\n1. 系统音量是否开启\n2. 音频设备是否正常\n3. 音频文件是否损坏")

        except Exception as e:
            print(f"❌ 强制播放音频异常: {e}")
            messagebox.showerror("错误", f"强制播放音频异常: {e}")

    def test_audio_sync(self):
        """测试音频同步功能"""
        print("🔄 开始测试音频同步...")

        if not self.cap or not self.audio_file_path:
            messagebox.showwarning("警告", "请先加载视频文件")
            return

        try:
            # 获取当前视频位置
            current_frame = self.current_frame
            current_time = current_frame / self.fps if self.fps > 0 else 0

            print(f"🔄 当前视频位置: 帧{current_frame}, 时间{current_time:.2f}秒")

            # 测试跳转到不同位置
            test_positions = [0.25, 0.5, 0.75]  # 25%, 50%, 75%位置

            for i, pos_ratio in enumerate(test_positions):
                test_frame = int(self.total_frames * pos_ratio)
                test_time = test_frame / self.fps if self.fps > 0 else 0

                print(f"🔄 测试跳转 {i+1}/3: 到{pos_ratio*100:.0f}%位置 (帧{test_frame}, {test_time:.2f}秒)")

                # 跳转视频
                self.seek_video(test_frame)

                # 等待一下
                time.sleep(1)

                # 检查音频状态
                if PYGAME_AVAILABLE and pygame.mixer.music.get_busy():
                    print(f"✅ 音频正在播放")
                else:
                    print(f"⚠️ 音频未播放")

            # 回到原位置
            print(f"🔄 回到原位置: 帧{current_frame}")
            self.seek_video(current_frame)

            messagebox.showinfo("完成", "音频同步测试完成！\n请检查控制台输出查看详细信息。")

        except Exception as e:
            print(f"❌ 音频同步测试失败: {e}")
            messagebox.showerror("错误", f"音频同步测试失败: {e}")

    def on_closing(self):
        """程序关闭时的清理工作"""
        self.stop_video()

        # 停止音频
        self.stop_audio()

        # 清理音频文件
        if self.audio_file_path and os.path.exists(self.audio_file_path):
            try:
                os.unlink(self.audio_file_path)
                print("🧹 清理临时音频文件")
            except:
                pass

        if self.cap:
            self.cap.release()
        self.root.destroy()


def main():
    """主函数"""
    print("=" * 60)
    print("🎬 简化版MP4播放器 - 带实时字幕生成")
    print("=" * 60)
    print("功能: 视频播放 + AI字幕生成 + 实时字幕显示")
    print("特点: 简单易用，支持中文语音识别")
    print("引擎: faster-whisper + moviepy")
    print("字幕: 演示模式 + AI生成模式")
    print("=" * 60)

    root = tk.Tk()
    app = SimpleMP4Player(root)

    # 设置关闭事件
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main()
