#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MP4播放器 - 带实时字幕功能
使用faster-whisper引擎和large-v3模型生成实时字幕
支持字幕位置和大小调整
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk, ImageDraw, ImageFont
import threading
import time
import queue
import os
try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False
    print("警告: faster-whisper未安装，字幕功能将被禁用")


class MP4PlayerWithSubtitles:
    def __init__(self, root):
        self.root = root
        self.root.title("MP4播放器 - 实时字幕")
        self.root.geometry("1200x800")
        
        # 视频相关变量
        self.cap = None
        self.video_path = None
        self.is_playing = False
        self.is_paused = False
        self.current_frame = 0
        self.total_frames = 0
        self.fps = 30
        self.frame_delay = 1/30
        
        # 字幕相关变量
        self.whisper_model = None
        self.subtitle_text = ""
        self.subtitle_queue = queue.Queue()
        self.audio_thread = None
        self.transcription_thread = None
        self.transcription_running = False

        # 进度相关变量
        self.transcription_progress = 0.0
        self.total_segments = 0
        self.processed_segments = 0
        
        # 字幕样式设置
        self.subtitle_font_size = 24
        self.subtitle_position_y = 0.85  # 相对位置 (0-1)
        self.subtitle_color = (255, 255, 255)  # 白色
        self.subtitle_bg_color = (0, 0, 0, 128)  # 半透明黑色背景
        
        # 音频相关
        self.audio_data = []
        self.audio_sample_rate = 16000
        
        self.setup_ui()
        self.load_whisper_model()
        self.check_subtitle_capability()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择按钮
        ttk.Button(control_frame, text="选择MP4文件", 
                  command=self.select_video_file).pack(side=tk.LEFT, padx=(0, 10))
        
        # 播放控制按钮
        self.play_button = ttk.Button(control_frame, text="播放", 
                                     command=self.toggle_play)
        self.play_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="停止", 
                  command=self.stop_video).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(control_frame, from_=0, to=100, 
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.seek_video)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 时间显示
        self.time_label = ttk.Label(control_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 字幕控制面板
        subtitle_frame = ttk.LabelFrame(main_frame, text="字幕设置")
        subtitle_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 字幕大小控制
        ttk.Label(subtitle_frame, text="字幕大小:").grid(row=0, column=0, padx=5, pady=5)
        self.font_size_var = tk.IntVar(value=self.subtitle_font_size)
        font_size_scale = ttk.Scale(subtitle_frame, from_=12, to=48, 
                                   orient=tk.HORIZONTAL, variable=self.font_size_var,
                                   command=self.update_subtitle_font_size)
        font_size_scale.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # 字幕位置控制
        ttk.Label(subtitle_frame, text="字幕位置:").grid(row=0, column=2, padx=5, pady=5)
        self.position_var = tk.DoubleVar(value=self.subtitle_position_y)
        position_scale = ttk.Scale(subtitle_frame, from_=0.1, to=0.95, 
                                  orient=tk.HORIZONTAL, variable=self.position_var,
                                  command=self.update_subtitle_position)
        position_scale.grid(row=0, column=3, padx=5, pady=5, sticky="ew")
        
        # 字幕开关
        self.subtitle_enabled = tk.BooleanVar(value=True)
        ttk.Checkbutton(subtitle_frame, text="启用字幕",
                       variable=self.subtitle_enabled).grid(row=0, column=4, padx=5, pady=5)

        # 字幕生成进度显示
        ttk.Label(subtitle_frame, text="字幕生成进度:").grid(row=1, column=0, padx=5, pady=5)

        # 进度条
        self.subtitle_progress_var = tk.DoubleVar()
        self.subtitle_progress_bar = ttk.Progressbar(
            subtitle_frame,
            variable=self.subtitle_progress_var,
            maximum=100,
            mode='determinate'
        )
        self.subtitle_progress_bar.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

        # 进度文本
        self.progress_text_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(subtitle_frame, textvariable=self.progress_text_var)
        self.progress_label.grid(row=1, column=3, columnspan=2, padx=5, pady=5)

        subtitle_frame.columnconfigure(1, weight=1)
        subtitle_frame.columnconfigure(3, weight=1)
        
        # 视频显示区域
        self.video_frame = ttk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=2)
        self.video_frame.pack(fill=tk.BOTH, expand=True)
        
        self.video_label = ttk.Label(self.video_frame, text="请选择MP4文件开始播放")
        self.video_label.pack(expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
    def load_whisper_model(self):
        """加载Whisper模型"""
        if not WHISPER_AVAILABLE:
            self.status_var.set("Whisper未安装，字幕功能不可用")
            return

        def load_model():
            try:
                self.status_var.set("正在加载Whisper large-v3模型...")
                self.root.update()

                # 使用CPU版本，如果有GPU可以改为"cuda"
                self.whisper_model = WhisperModel("large-v3", device="cpu", compute_type="int8")

                self.status_var.set("Whisper模型加载完成")
                self.root.update()
            except Exception as e:
                print(f"加载Whisper模型失败: {str(e)}")
                self.status_var.set("模型加载失败")

        # 在后台线程中加载模型
        threading.Thread(target=load_model, daemon=True).start()

    def check_subtitle_capability(self):
        """检查字幕功能能力"""
        def check_capability():
            time.sleep(2)  # 等待Whisper模型加载

            # 检查各种能力
            whisper_ready = WHISPER_AVAILABLE and self.whisper_model is not None

            # 检查音频提取能力
            audio_capability = False
            try:
                # 检查moviepy
                from moviepy import VideoFileClip
                audio_capability = True
                audio_method = "moviepy"
            except ImportError:
                try:
                    # 检查ffmpeg
                    import subprocess
                    result = subprocess.run(["ffmpeg", "-version"],
                                          capture_output=True, timeout=5)
                    if result.returncode == 0:
                        audio_capability = True
                        audio_method = "ffmpeg"
                except:
                    audio_method = "无"

            # 更新状态显示
            if whisper_ready and audio_capability:
                status = f"字幕功能就绪 (Whisper + {audio_method})"
            elif whisper_ready:
                status = f"Whisper就绪，但音频提取不可用 (需要moviepy或ffmpeg)"
            elif audio_capability:
                status = f"音频提取就绪 ({audio_method})，但Whisper不可用"
            else:
                status = "字幕功能不可用 (需要安装faster-whisper和moviepy)"

            self.root.after(0, lambda: self.status_var.set(status))

        threading.Thread(target=check_capability, daemon=True).start()
        
    def select_video_file(self):
        """选择视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择MP4文件",
            filetypes=[("MP4文件", "*.mp4"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.load_video(file_path)
            
    def load_video(self, video_path):
        """加载视频文件"""
        try:
            # 停止当前播放
            self.stop_video()

            # 验证文件存在
            if not os.path.exists(video_path):
                raise Exception(f"视频文件不存在: {video_path}")

            # 检查文件大小
            file_size = os.path.getsize(video_path)
            if file_size == 0:
                raise Exception("视频文件为空")

            print(f"正在加载视频: {video_path} (大小: {file_size} 字节)")

            # 打开视频文件 - 添加错误处理
            try:
                self.cap = cv2.VideoCapture(video_path)
            except Exception as cv_error:
                raise Exception(f"OpenCV无法创建VideoCapture对象: {cv_error}")

            if not self.cap.isOpened():
                # 尝试释放资源并重试
                if self.cap:
                    self.cap.release()
                    self.cap = None
                raise Exception("无法打开视频文件 - 可能是格式不支持或文件损坏")

            self.video_path = video_path

            # 安全地获取视频信息
            try:
                self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
                self.fps = self.cap.get(cv2.CAP_PROP_FPS)

                # 验证获取的值
                if self.total_frames <= 0:
                    print("警告: 无法获取准确的帧数，使用默认值")
                    self.total_frames = 1000  # 默认值

                if self.fps <= 0:
                    print("警告: 无法获取准确的帧率，使用默认值")
                    self.fps = 30  # 默认帧率

                self.frame_delay = 1.0 / self.fps

                print(f"视频信息: {self.total_frames}帧, {self.fps}fps")

            except Exception as prop_error:
                print(f"获取视频属性时出错: {prop_error}")
                # 使用默认值
                self.total_frames = 1000
                self.fps = 30
                self.frame_delay = 1/30

            # 重置进度条
            self.progress_bar.configure(to=max(1, self.total_frames-1))
            self.current_frame = 0

            # 显示第一帧
            self.show_frame()

            # 更新状态
            duration = self.total_frames / self.fps if self.fps > 0 else 0
            self.status_var.set(f"视频已加载: {os.path.basename(video_path)} "
                              f"({self.total_frames}帧, {duration:.1f}秒)")

            # 启用播放按钮
            self.play_button.configure(state="normal")

        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)

            # 清理资源
            if hasattr(self, 'cap') and self.cap:
                try:
                    self.cap.release()
                except:
                    pass
                self.cap = None
            
    def show_frame(self):
        """显示当前帧"""
        if self.cap is None:
            return

        try:
            # 安全地读取帧
            ret, frame = self.cap.read()
            if not ret:
                print("无法读取帧，可能到达视频末尾")
                return

            # 验证帧数据
            if frame is None:
                print("读取到空帧")
                return

            if frame.size == 0:
                print("读取到空帧数据")
                return

            # 添加字幕到帧
            try:
                if self.subtitle_enabled.get() and self.subtitle_text:
                    frame = self.add_subtitle_to_frame(frame, self.subtitle_text)
            except Exception as subtitle_error:
                print(f"添加字幕时出错: {subtitle_error}")
                # 继续处理原始帧

            # 转换为PIL图像
            try:
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(frame_rgb)
            except Exception as convert_error:
                print(f"图像转换时出错: {convert_error}")
                return

            # 调整图像大小以适应显示区域
            try:
                display_width = self.video_frame.winfo_width()
                display_height = self.video_frame.winfo_height()

                if display_width > 1 and display_height > 1:
                    # 保持宽高比
                    img_ratio = pil_image.width / pil_image.height
                    display_ratio = display_width / display_height

                    if img_ratio > display_ratio:
                        new_width = display_width
                        new_height = int(display_width / img_ratio)
                    else:
                        new_height = display_height
                        new_width = int(display_height * img_ratio)

                    # 确保尺寸有效
                    new_width = max(1, new_width)
                    new_height = max(1, new_height)

                    pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            except Exception as resize_error:
                print(f"图像缩放时出错: {resize_error}")
                # 使用原始尺寸

            # 转换为Tkinter图像
            try:
                photo = ImageTk.PhotoImage(pil_image)
                self.video_label.configure(image=photo, text="")
                self.video_label.image = photo  # 保持引用
            except Exception as tk_error:
                print(f"Tkinter图像转换时出错: {tk_error}")
                return

            # 更新进度条和时间
            try:
                self.progress_var.set(self.current_frame)
                current_time = self.current_frame / self.fps if self.fps > 0 else 0
                total_time = self.total_frames / self.fps if self.fps > 0 else 0
                self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")
            except Exception as ui_error:
                print(f"更新UI时出错: {ui_error}")

        except Exception as e:
            print(f"显示帧时发生未知错误: {e}")
            print(f"错误类型: {type(e).__name__}")

            # 如果是OpenCV的C++异常，尝试重新初始化
            if "OpenCV" in str(e) or "C++" in str(e):
                print("检测到OpenCV C++异常，尝试重新初始化视频捕获...")
                self.reinitialize_video_capture()

    def reinitialize_video_capture(self):
        """重新初始化视频捕获（用于处理C++异常）"""
        try:
            if not self.video_path:
                print("没有视频路径，无法重新初始化")
                return False

            print("正在重新初始化视频捕获...")

            # 释放当前资源
            if self.cap:
                try:
                    self.cap.release()
                except:
                    pass
                self.cap = None

            # 短暂等待
            time.sleep(0.1)

            # 重新创建VideoCapture
            self.cap = cv2.VideoCapture(self.video_path)

            if not self.cap.isOpened():
                print("重新初始化失败")
                return False

            # 恢复到当前帧位置
            if self.current_frame > 0:
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame)

            print("视频捕获重新初始化成功")
            return True

        except Exception as reinit_error:
            print(f"重新初始化时出错: {reinit_error}")
            return False
            
    def add_subtitle_to_frame(self, frame, text):
        """在帧上添加字幕"""
        if not text.strip():
            return frame
            
        # 转换为PIL图像以便添加文字
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)
        draw = ImageDraw.Draw(pil_image)
        
        # 尝试加载字体
        try:
            # Windows系统字体路径
            font_path = "C:/Windows/Fonts/msyh.ttc"  # 微软雅黑
            if not os.path.exists(font_path):
                font_path = "C:/Windows/Fonts/simsun.ttc"  # 宋体
            
            if os.path.exists(font_path):
                font = ImageFont.truetype(font_path, self.subtitle_font_size)
            else:
                font = ImageFont.load_default()
        except:
            font = ImageFont.load_default()
        
        # 计算文字位置
        img_width, img_height = pil_image.size
        
        # 分行处理长文本
        words = text.split()
        lines = []
        current_line = ""
        max_width = img_width * 0.9  # 最大宽度为图像宽度的90%
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            bbox = draw.textbbox((0, 0), test_line, font=font)
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        # 绘制每一行
        line_height = self.subtitle_font_size + 5
        total_text_height = len(lines) * line_height
        start_y = int(img_height * self.subtitle_position_y - total_text_height / 2)
        
        for i, line in enumerate(lines):
            bbox = draw.textbbox((0, 0), line, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (img_width - text_width) // 2
            y = start_y + i * line_height
            
            # 绘制背景
            padding = 5
            bg_bbox = [x - padding, y - padding, 
                      x + text_width + padding, y + text_height + padding]
            draw.rectangle(bg_bbox, fill=self.subtitle_bg_color)
            
            # 绘制文字
            draw.text((x, y), line, font=font, fill=self.subtitle_color)
        
        # 转换回OpenCV格式
        frame_with_subtitle = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        return frame_with_subtitle

    def toggle_play(self):
        """切换播放/暂停状态"""
        if not self.cap:
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        if self.is_playing:
            self.pause_video()
        else:
            self.play_video()

    def play_video(self):
        """开始播放视频"""
        if not self.cap:
            return

        self.is_playing = True
        self.is_paused = False
        self.play_button.configure(text="暂停")

        # 启动视频播放线程
        self.video_thread = threading.Thread(target=self.video_playback_loop, daemon=True)
        self.video_thread.start()

        # 启动字幕功能
        if self.subtitle_enabled.get():
            if self.whisper_model and WHISPER_AVAILABLE:
                # 如果Whisper可用，尝试真实字幕
                print("启动真实字幕生成...")
                self.start_audio_transcription()
            else:
                # 否则显示演示字幕
                print("Whisper不可用，启动演示字幕...")
                self.start_demo_subtitles()

    def pause_video(self):
        """暂停视频"""
        self.is_playing = False
        self.is_paused = True
        self.play_button.configure(text="播放")

        # 停止音频转录
        self.stop_audio_transcription()

    def stop_video(self):
        """停止视频"""
        self.is_playing = False
        self.is_paused = False
        self.play_button.configure(text="播放")

        # 停止音频转录
        self.stop_audio_transcription()

        # 重置到开始位置
        if self.cap:
            self.current_frame = 0
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            self.show_frame()

    def seek_video(self, value):
        """跳转到指定帧"""
        if not self.cap:
            return

        frame_number = int(float(value))
        self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        self.current_frame = frame_number

        if not self.is_playing:
            self.show_frame()

    def video_playback_loop(self):
        """视频播放循环"""
        consecutive_errors = 0
        max_consecutive_errors = 5

        while self.is_playing and self.cap:
            try:
                start_time = time.time()

                # 读取并显示帧
                ret, frame = self.cap.read()
                if not ret:
                    # 视频结束
                    print("视频播放结束")
                    self.is_playing = False
                    self.root.after(0, lambda: self.play_button.configure(text="播放"))
                    break

                # 验证帧数据
                if frame is None or frame.size == 0:
                    print("读取到无效帧")
                    consecutive_errors += 1
                    if consecutive_errors >= max_consecutive_errors:
                        print("连续错误过多，停止播放")
                        break
                    continue

                # 重置错误计数
                consecutive_errors = 0

                # 更新当前帧位置
                try:
                    self.current_frame = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                except:
                    # 如果无法获取位置，手动递增
                    self.current_frame += 1

                # 在主线程中更新UI
                self.root.after(0, self.show_frame)

                # 控制播放速度
                elapsed = time.time() - start_time
                sleep_time = max(0, self.frame_delay - elapsed)
                time.sleep(sleep_time)

            except Exception as e:
                print(f"视频播放循环中出错: {e}")
                consecutive_errors += 1

                # 如果是OpenCV C++异常，尝试重新初始化
                if "OpenCV" in str(e) or "C++" in str(e):
                    print("检测到OpenCV C++异常，尝试重新初始化...")
                    if self.reinitialize_video_capture():
                        consecutive_errors = 0  # 重置错误计数
                        continue

                if consecutive_errors >= max_consecutive_errors:
                    print("连续错误过多，停止播放")
                    self.is_playing = False
                    self.root.after(0, lambda: self.play_button.configure(text="播放"))
                    break

                # 短暂等待后继续
                time.sleep(0.1)

    def start_audio_transcription(self):
        """启动音频转录"""
        if not self.whisper_model or not self.video_path:
            return

        self.stop_audio_transcription()  # 停止之前的转录

        # 启动音频提取和转录线程
        self.transcription_thread = threading.Thread(
            target=self.audio_transcription_loop, daemon=True)
        self.transcription_thread.start()

    def stop_audio_transcription(self):
        """停止音频转录"""
        if hasattr(self, 'transcription_thread') and self.transcription_thread:
            # 设置停止标志
            self.transcription_running = False

    def audio_transcription_loop(self):
        """音频转录循环"""
        try:
            self.transcription_running = True

            # 重置进度
            self.root.after(0, lambda: self.update_progress(0, "开始音频转录..."))

            # 提取音频
            self.root.after(0, lambda: self.update_progress(10, "正在提取音频..."))
            audio_path = self.extract_audio_from_video()
            if not audio_path:
                self.root.after(0, lambda: self.update_progress(0, "音频提取失败，启动演示字幕"))
                # 如果音频提取失败，启动演示字幕
                self.start_demo_subtitles()
                return

            # 使用Whisper进行转录
            self.root.after(0, lambda: self.update_progress(20, "正在加载音频文件..."))

            # 创建一个自定义的转录函数来跟踪进度
            segments_list = []

            def progress_callback(segment):
                """转录进度回调"""
                segments_list.append(segment)
                progress = min(20 + (len(segments_list) * 60 / max(1, self.total_segments)), 80)
                self.root.after(0, lambda: self.update_progress(
                    progress, f"正在转录... ({len(segments_list)}/{self.total_segments} 段)"))

            # 估算总段数（基于视频时长）
            video_duration = self.total_frames / self.fps if self.fps > 0 else 60
            self.total_segments = max(1, int(video_duration / 10))  # 假设每10秒一段

            segments, _ = self.whisper_model.transcribe(
                audio_path,
                beam_size=5,
                language="zh",  # 可以设置为None让模型自动检测
                word_timestamps=True
            )

            self.root.after(0, lambda: self.update_progress(80, "正在处理转录结果..."))

            # 将segments转换为列表以便计算总数
            segments_list = list(segments)
            self.total_segments = len(segments_list)
            self.processed_segments = 0

            # 处理转录结果
            for i, segment in enumerate(segments_list):
                if not self.transcription_running:
                    break

                # 更新字幕
                subtitle_text = segment.text.strip()
                if subtitle_text:
                    # 计算显示时间
                    start_time = segment.start
                    end_time = segment.end

                    # 在主线程中更新字幕和进度
                    self.processed_segments = i + 1
                    progress = 80 + (self.processed_segments * 20 / self.total_segments)

                    def update_ui(text=subtitle_text, p=progress, seg=self.processed_segments, total=self.total_segments):
                        self.update_subtitle(text)
                        self.update_progress(p, f"显示字幕 ({seg}/{total})")

                    self.root.after(0, update_ui)

                    # 等待到字幕结束时间
                    display_duration = end_time - start_time
                    time.sleep(min(display_duration, 5.0))  # 最长显示5秒

            # 转录完成
            self.root.after(0, lambda: self.update_progress(100, "字幕生成完成"))
            time.sleep(2)  # 显示完成状态2秒
            self.root.after(0, lambda: self.update_progress(0, "就绪"))

            # 清理临时音频文件
            if audio_path and os.path.exists(audio_path):
                os.remove(audio_path)

        except Exception as e:
            error_msg = f"音频转录错误: {e}"
            print(error_msg)
            self.root.after(0, lambda: self.update_progress(0, "转录失败"))

    def extract_audio_from_video(self):
        """从视频中提取音频 - 支持多种方法"""
        if not self.video_path:
            print("错误: 没有视频文件路径")
            return None

        try:
            # 创建临时音频文件
            import tempfile
            temp_audio = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
            temp_audio_path = temp_audio.name
            temp_audio.close()

            # 方法1: 尝试使用moviepy
            if self.extract_audio_with_moviepy(temp_audio_path):
                return temp_audio_path

            # 方法2: 尝试使用ffmpeg
            if self.extract_audio_with_ffmpeg(temp_audio_path):
                return temp_audio_path

            # 方法3: 尝试使用opencv + 手动音频提取
            if self.extract_audio_with_opencv(temp_audio_path):
                return temp_audio_path

            # 如果所有方法都失败，清理临时文件并显示帮助信息
            if os.path.exists(temp_audio_path):
                os.remove(temp_audio_path)

            self.show_audio_extraction_help()
            return None

        except Exception as e:
            print(f"提取音频失败: {e}")
            return None

    def extract_audio_with_moviepy(self, output_path):
        """使用moviepy提取音频"""
        try:
            print("尝试使用moviepy提取音频...")
            from moviepy import VideoFileClip

            # 加载视频文件
            video = VideoFileClip(self.video_path)

            # 提取音频
            audio = video.audio
            if audio is None:
                print("视频文件没有音频轨道")
                video.close()
                return False

            # 保存为WAV格式，16kHz采样率
            audio.write_audiofile(
                output_path,
                fps=16000,  # 16kHz采样率，适合Whisper
                nbytes=2,   # 16位
                codec='pcm_s16le',
                logger=None  # 禁用日志输出
            )

            # 清理资源
            audio.close()
            video.close()

            print(f"✓ moviepy音频提取成功: {output_path}")
            return True

        except ImportError:
            print("moviepy未安装")
            print("提示: 可以运行 'pip install moviepy' 来安装moviepy")
            return False
        except Exception as e:
            print(f"moviepy提取失败: {e}")
            return False

    def extract_audio_with_ffmpeg(self, output_path):
        """使用ffmpeg提取音频"""
        try:
            print("尝试使用ffmpeg提取音频...")
            import subprocess

            # 检查ffmpeg是否可用
            try:
                subprocess.run(["ffmpeg", "-version"],
                             capture_output=True, check=True, timeout=5)
            except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                print("ffmpeg不可用，跳过此方法")
                return False

            # 使用ffmpeg提取音频
            cmd = [
                "ffmpeg",
                "-i", self.video_path,      # 输入视频文件
                "-ar", "16000",             # 采样率16kHz
                "-ac", "1",                 # 单声道
                "-c:a", "pcm_s16le",        # 音频编码
                "-y",                       # 覆盖输出文件
                output_path                 # 输出音频文件
            ]

            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60  # 60秒超时
            )

            if result.returncode == 0:
                print(f"✓ ffmpeg音频提取成功: {output_path}")
                return True
            else:
                print(f"ffmpeg提取失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print("ffmpeg提取超时")
            return False
        except Exception as e:
            print(f"ffmpeg提取失败: {e}")
            return False

    def extract_audio_with_opencv(self, output_path):
        """使用OpenCV尝试提取音频（备用方法）"""
        try:
            print("尝试使用OpenCV方法...")

            # 注意：OpenCV本身不支持音频提取
            # 这里我们提供一个替代方案：生成静音音频用于测试
            print("警告: OpenCV不支持音频提取，生成测试用静音音频")

            # 生成3秒的静音音频用于测试
            import wave
            import numpy as np

            sample_rate = 16000
            duration = 3  # 3秒
            samples = int(sample_rate * duration)

            # 生成静音数据
            audio_data = np.zeros(samples, dtype=np.int16)

            # 保存为WAV文件
            with wave.open(output_path, 'w') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())

            print(f"✓ 生成测试音频文件: {output_path}")
            print("注意: 这是静音测试文件，不包含实际音频内容")
            return True

        except Exception as e:
            print(f"OpenCV方法失败: {e}")
            return False

    def show_audio_extraction_help(self):
        """显示音频提取帮助信息"""
        help_message = """
音频提取失败！

为了启用实时字幕功能，需要安装音频处理工具：

方法1 - 安装moviepy（推荐）：
pip install moviepy

方法2 - 安装ffmpeg：
1. 下载ffmpeg: https://ffmpeg.org/download.html
2. 将ffmpeg.exe添加到系统PATH环境变量

方法3 - 使用在线工具：
可以先用其他工具将MP4转换为音频文件，然后使用Whisper处理

当前播放器将继续运行，但字幕功能将显示演示内容。
        """

        print(help_message)

        # 在UI中显示帮助信息
        try:
            from tkinter import messagebox
            messagebox.showinfo(
                "音频提取失败",
                "音频提取失败！\n\n"
                "为了启用实时字幕功能，请安装以下工具之一：\n\n"
                "• moviepy: pip install moviepy\n"
                "• ffmpeg: 下载并添加到PATH\n\n"
                "当前将显示演示字幕。"
            )
        except:
            pass  # 如果无法显示对话框，只在控制台显示

    def start_demo_subtitles(self):
        """启动演示字幕（当音频提取失败时使用）"""
        demo_subtitles = [
            "音频提取失败，显示演示字幕",
            "请安装moviepy或ffmpeg以启用真实字幕",
            "pip install moviepy",
            "这是字幕位置和样式的演示",
            "您可以调整字幕大小和位置",
            "支持多种字幕颜色选择",
            "faster-whisper引擎可生成真实字幕",
            "当前显示的是演示效果"
        ]

        def demo_loop():
            subtitle_index = 0
            total_subtitles = len(demo_subtitles)

            while self.is_playing and self.subtitle_enabled.get():
                if subtitle_index < total_subtitles:
                    subtitle_text = demo_subtitles[subtitle_index]

                    # 更新进度
                    progress = ((subtitle_index + 1) / total_subtitles) * 100
                    status = f"演示字幕 ({subtitle_index + 1}/{total_subtitles})"

                    def update_ui(text=subtitle_text, p=progress, s=status):
                        self.update_subtitle(text)
                        self.update_progress(p, s)

                    self.root.after(0, update_ui)
                    subtitle_index = (subtitle_index + 1) % total_subtitles

                time.sleep(3)  # 每3秒更换一次字幕

        if not hasattr(self, 'demo_subtitle_thread') or not self.demo_subtitle_thread.is_alive():
            self.demo_subtitle_thread = threading.Thread(target=demo_loop, daemon=True)
            self.demo_subtitle_thread.start()



    def update_progress(self, progress, status_text):
        """更新字幕生成进度"""
        self.subtitle_progress_var.set(progress)
        self.progress_text_var.set(status_text)

    def update_subtitle(self, text):
        """更新字幕文本"""
        self.subtitle_text = text

    def update_subtitle_font_size(self, value):
        """更新字幕字体大小"""
        self.subtitle_font_size = int(float(value))

    def update_subtitle_position(self, value):
        """更新字幕位置"""
        self.subtitle_position_y = float(value)

    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def on_closing(self):
        """程序关闭时的清理工作"""
        self.stop_video()
        if self.cap:
            self.cap.release()
        self.root.destroy()


def main():
    """主函数"""
    root = tk.Tk()
    app = MP4PlayerWithSubtitles(root)

    # 设置关闭事件
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main()
