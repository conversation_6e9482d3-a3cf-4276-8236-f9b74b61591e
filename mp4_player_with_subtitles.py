#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MP4播放器 - 带实时字幕功能
使用faster-whisper引擎和large-v3模型生成实时字幕
支持字幕位置和大小调整

🛡️ 已修复OpenCV MSMF后端崩溃问题:
- 问题: cap_msmf.cpp:124 Assertion failed (p == NULL)
- 解决: 完全禁用MSMF后端，优先使用FFMPEG
- 保护: 环境变量 + 后端验证 + 智能恢复
- 结果: 支持长时间播放和循环播放，不会自动关闭

修复内容:
1. 环境变量禁用MSMF (在导入OpenCV前)
2. 后端选择优先级调整 (FFMPEG > DSHOW > 其他)
3. 运行时MSMF检测和阻止
4. 播放循环中的MSMF错误检测
5. 紧急恢复机制 (自动切换到安全后端)
"""

import os
import sys

# 🛡️ 在导入OpenCV之前完全禁用MSMF后端，防止崩溃
# 这是解决"cap_msmf.cpp:124 Assertion failed"问题的关键
os.environ['OPENCV_VIDEOIO_MSMF_ENABLE_HW_TRANSFORMS'] = '0'
os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
os.environ['OPENCV_VIDEOIO_DEBUG'] = '1'  # 启用调试信息

# 🔧 专门解决pthread_frame.c:173错误的环境变量
# 这是解决"Assertion fctx->async_lock failed"问题的关键
os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1'
os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG'
os.environ['FFMPEG_THREAD_SAFE'] = '1'
os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'
os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '1'

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk, ImageDraw, ImageFont
import threading
import time
import queue
import os
import sys
try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
    print(f"✓ faster-whisper 导入成功")
except ImportError as e:
    WHISPER_AVAILABLE = False
    print(f"警告: faster-whisper未安装，字幕功能将被禁用")
    print(f"导入错误详情: {e}")
    print(f"Python 路径: {sys.executable}")
except Exception as e:
    WHISPER_AVAILABLE = False
    print(f"警告: faster-whisper 导入时发生错误: {e}")
    print(f"Python 路径: {sys.executable}")


class MP4PlayerWithSubtitles:
    def __init__(self, root):
        try:
            print("🔧 初始化播放器组件...")

            self.root = root
            self.root.title("MP4播放器 - 实时字幕")
            self.root.geometry("1200x800")

            # 视频相关变量
            self.cap = None
            self.video_path = None
            self.is_playing = False
            self.is_paused = False
            self.current_frame = 0
            self.total_frames = 0
            self.fps = 30
            self.frame_delay = 1/30

            # 字幕相关变量
            self.whisper_model = None
            self.subtitle_text = ""
            self.subtitle_queue = queue.Queue()
            self.audio_thread = None
            self.transcription_thread = None
            self.transcription_running = False

            # 进度相关变量
            self.transcription_progress = 0.0
            self.total_segments = 0
            self.processed_segments = 0

            # 字幕样式设置
            self.subtitle_font_size = 24
            self.subtitle_position_y = 0.85  # 相对位置 (0-1)
            self.subtitle_color = (255, 255, 255)  # 白色
            self.subtitle_bg_color = (0, 0, 0, 128)  # 半透明黑色背景

            # FFmpeg错误监控
            self.ffmpeg_error_risk = False

            # 音频相关
            self.audio_data = []
            self.audio_sample_rate = 16000

            print("🎨 创建用户界面...")
            self.setup_ui()

            print("🤖 初始化Whisper模型...")
            self.load_whisper_model()

            print("📝 检查字幕功能...")
            self.check_subtitle_capability()

            print("✅ 播放器初始化完成")

        except Exception as e:
            print(f"❌ 播放器初始化失败: {e}")
            import traceback
            traceback.print_exc()

            # 尝试显示错误信息给用户
            try:
                import tkinter.messagebox as msgbox
                msgbox.showerror("初始化错误", f"播放器初始化失败:\n{e}\n\n请检查控制台输出获取详细信息。")
            except:
                pass

            raise
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 文件选择按钮
        ttk.Button(control_frame, text="选择MP4文件", 
                  command=self.select_video_file).pack(side=tk.LEFT, padx=(0, 10))
        
        # 播放控制按钮
        self.play_button = ttk.Button(control_frame, text="播放", 
                                     command=self.toggle_play)
        self.play_button.pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(control_frame, text="停止", 
                  command=self.stop_video).pack(side=tk.LEFT, padx=(0, 10))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Scale(control_frame, from_=0, to=100, 
                                     orient=tk.HORIZONTAL, variable=self.progress_var,
                                     command=self.seek_video)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        # 时间显示
        self.time_label = ttk.Label(control_frame, text="00:00 / 00:00")
        self.time_label.pack(side=tk.RIGHT)
        
        # 字幕控制面板
        subtitle_frame = ttk.LabelFrame(main_frame, text="字幕设置")
        subtitle_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 字幕大小控制
        ttk.Label(subtitle_frame, text="字幕大小:").grid(row=0, column=0, padx=5, pady=5)
        self.font_size_var = tk.IntVar(value=self.subtitle_font_size)
        font_size_scale = ttk.Scale(subtitle_frame, from_=12, to=48, 
                                   orient=tk.HORIZONTAL, variable=self.font_size_var,
                                   command=self.update_subtitle_font_size)
        font_size_scale.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # 字幕位置控制
        ttk.Label(subtitle_frame, text="字幕位置:").grid(row=0, column=2, padx=5, pady=5)
        self.position_var = tk.DoubleVar(value=self.subtitle_position_y)
        position_scale = ttk.Scale(subtitle_frame, from_=0.1, to=0.95, 
                                  orient=tk.HORIZONTAL, variable=self.position_var,
                                  command=self.update_subtitle_position)
        position_scale.grid(row=0, column=3, padx=5, pady=5, sticky="ew")
        
        # 字幕开关
        self.subtitle_enabled = tk.BooleanVar(value=True)
        ttk.Checkbutton(subtitle_frame, text="启用字幕",
                       variable=self.subtitle_enabled).grid(row=0, column=4, padx=5, pady=5)

        # 字幕生成进度显示
        ttk.Label(subtitle_frame, text="字幕生成进度:").grid(row=1, column=0, padx=5, pady=5)

        # 进度条
        self.subtitle_progress_var = tk.DoubleVar()
        self.subtitle_progress_bar = ttk.Progressbar(
            subtitle_frame,
            variable=self.subtitle_progress_var,
            maximum=100,
            mode='determinate'
        )
        self.subtitle_progress_bar.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

        # 进度文本
        self.progress_text_var = tk.StringVar(value="就绪")
        self.progress_label = ttk.Label(subtitle_frame, textvariable=self.progress_text_var)
        self.progress_label.grid(row=1, column=3, columnspan=2, padx=5, pady=5)

        subtitle_frame.columnconfigure(1, weight=1)
        subtitle_frame.columnconfigure(3, weight=1)
        
        # 视频显示区域
        self.video_frame = ttk.Frame(main_frame, relief=tk.SUNKEN, borderwidth=2)
        self.video_frame.pack(fill=tk.BOTH, expand=True)
        
        self.video_label = ttk.Label(self.video_frame, text="请选择MP4文件开始播放")
        self.video_label.pack(expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
        
    def load_whisper_model(self):
        """加载Whisper模型"""
        if not WHISPER_AVAILABLE:
            self.status_var.set("Whisper未安装，字幕功能不可用")
            return

        def load_model():
            try:
                print("开始加载Whisper模型...")

                # 安全地更新状态
                try:
                    self.root.after(0, lambda: self.status_var.set("正在加载Whisper模型..."))
                except:
                    pass

                # 首先尝试加载较小的模型
                model_options = [
                    ("base", "base模型 (较快)"),
                    ("small", "small模型 (平衡)"),
                    ("medium", "medium模型 (较好)"),
                    ("large-v3", "large-v3模型 (最好)")
                ]

                model_loaded = False
                for model_name, model_desc in model_options:
                    try:
                        print(f"尝试加载 {model_desc}...")

                        # 安全地更新状态
                        try:
                            self.root.after(0, lambda desc=model_desc: self.status_var.set(f"正在加载 {desc}..."))
                        except:
                            pass

                        # 使用CPU版本，确保兼容性
                        self.whisper_model = WhisperModel(model_name, device="cpu", compute_type="int8")

                        success_msg = f"✓ {model_desc} 加载成功"
                        print(success_msg)

                        # 安全地更新状态
                        try:
                            self.root.after(0, lambda msg=success_msg: self.status_var.set(msg))
                        except:
                            pass

                        model_loaded = True
                        break

                    except Exception as model_error:
                        print(f"加载 {model_desc} 失败: {model_error}")
                        continue

                if not model_loaded:
                    raise Exception("所有模型加载尝试都失败")

            except Exception as e:
                error_msg = f"Whisper模型加载失败: {str(e)}"
                print(error_msg)

                # 安全地更新状态
                try:
                    self.root.after(0, lambda: self.status_var.set("模型加载失败"))
                except:
                    pass

                # 提供解决建议
                if "download" in str(e).lower():
                    print("建议: 检查网络连接，模型需要从网络下载")
                elif "memory" in str(e).lower():
                    print("建议: 内存不足，尝试关闭其他程序")
                else:
                    print("建议: 检查 faster-whisper 安装是否正确")

        # 在后台线程中加载模型
        threading.Thread(target=load_model, daemon=True).start()

    def check_subtitle_capability(self):
        """检查字幕功能能力"""
        def check_capability():
            time.sleep(2)  # 等待Whisper模型加载

            # 检查各种能力
            whisper_ready = WHISPER_AVAILABLE and self.whisper_model is not None

            # 检查音频提取能力
            audio_capability = False
            try:
                # 检查moviepy
                from moviepy import VideoFileClip
                audio_capability = True
                audio_method = "moviepy"
            except ImportError:
                try:
                    # 检查ffmpeg
                    import subprocess
                    result = subprocess.run(["ffmpeg", "-version"],
                                          capture_output=True, timeout=5)
                    if result.returncode == 0:
                        audio_capability = True
                        audio_method = "ffmpeg"
                except:
                    audio_method = "无"

            # 更新状态显示
            if whisper_ready and audio_capability:
                status = f"字幕功能就绪 (Whisper + {audio_method})"
            elif whisper_ready:
                status = f"Whisper就绪，但音频提取不可用 (需要moviepy或ffmpeg)"
            elif audio_capability:
                status = f"音频提取就绪 ({audio_method})，但Whisper不可用"
            else:
                status = "字幕功能不可用 (需要安装faster-whisper和moviepy)"

            # 安全地更新状态
            try:
                self.root.after(0, lambda: self.status_var.set(status))
            except:
                print(f"状态更新: {status}")

        threading.Thread(target=check_capability, daemon=True).start()
        
    def select_video_file(self):
        """选择视频文件"""
        file_path = filedialog.askopenfilename(
            title="选择MP4文件",
            filetypes=[("MP4文件", "*.mp4"), ("所有文件", "*.*")]
        )
        
        if file_path:
            self.load_video(file_path)
            
    def load_video(self, video_path):
        """加载视频文件"""
        try:
            # 停止当前播放
            self.stop_video()

            # 验证文件存在
            if not os.path.exists(video_path):
                raise Exception(f"视频文件不存在: {video_path}")

            # 检查文件大小
            file_size = os.path.getsize(video_path)
            if file_size == 0:
                raise Exception("视频文件为空")

            print(f"正在加载视频: {video_path} (大小: {file_size} 字节)")

            # 设置视频路径（在创建capture之前）
            self.video_path = video_path

            # 打开视频文件 - 智能后端选择
            print("🔧 智能选择最佳视频后端...")
            self.cap = None

            # 🛡️ 使用安全的后端顺序，避免MSMF崩溃问题
            all_methods = [
                ("单线程FFMPEG（推荐）", self.create_single_thread_capture),
                ("标准FFMPEG", self.create_standard_ffmpeg_capture),
                ("DSHOW后端", self.create_dshow_capture),
                ("默认后端", self.create_default_capture),
                ("MSMF后端（已禁用）", self.create_msmf_capture),  # 已禁用，但保留用于说明
            ]

            for method_name, method_func in all_methods:
                try:
                    print(f"尝试 {method_name}...")
                    cap = method_func()
                    if cap and cap.isOpened():
                        # 验证能否读取帧
                        ret, test_frame = cap.read()
                        if ret and test_frame is not None:
                            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置位置
                            self.cap = cap
                            print(f"✓ {method_name} 加载成功")
                            break
                        else:
                            cap.release()
                            print(f"❌ {method_name} 无法读取帧")
                    else:
                        if cap:
                            cap.release()
                        print(f"❌ {method_name} 无法打开")
                except Exception as e:
                    print(f"❌ {method_name} 失败: {e}")
                    continue

            if not self.cap or not self.cap.isOpened():
                # 提供详细的诊断信息
                self.diagnose_loading_failure(video_path)
                raise Exception("所有视频后端都无法打开文件")

            # 安全地获取视频信息
            try:
                self.total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
                self.fps = self.cap.get(cv2.CAP_PROP_FPS)

                # 验证获取的值
                if self.total_frames <= 0:
                    print("警告: 无法获取准确的帧数，使用默认值")
                    self.total_frames = 1000  # 默认值

                if self.fps <= 0:
                    print("警告: 无法获取准确的帧率，使用默认值")
                    self.fps = 30  # 默认帧率

                self.frame_delay = 1.0 / self.fps

                print(f"视频信息: {self.total_frames}帧, {self.fps}fps")

            except Exception as prop_error:
                print(f"获取视频属性时出错: {prop_error}")
                # 使用默认值
                self.total_frames = 1000
                self.fps = 30
                self.frame_delay = 1/30

            # 重置进度条
            self.progress_bar.configure(to=max(1, self.total_frames-1))
            self.current_frame = 0

            # 显示第一帧
            self.show_frame()

            # 更新状态
            duration = self.total_frames / self.fps if self.fps > 0 else 0
            self.status_var.set(f"视频已加载: {os.path.basename(video_path)} "
                              f"({self.total_frames}帧, {duration:.1f}秒)")

            # 启用播放按钮
            self.play_button.configure(state="normal")

        except Exception as e:
            error_msg = f"加载视频失败: {str(e)}"
            print(error_msg)
            messagebox.showerror("错误", error_msg)

            # 清理资源
            if hasattr(self, 'cap') and self.cap:
                try:
                    self.cap.release()
                except:
                    pass
                self.cap = None
            
    def show_frame(self):
        """显示当前帧"""
        if self.cap is None:
            return

        try:
            # 安全地读取帧
            ret, frame = self.cap.read()
            if not ret:
                # 更详细的诊断信息
                current_pos = 0
                try:
                    current_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                except:
                    pass

                if current_pos >= self.total_frames - 1:
                    print(f"视频播放完毕 (帧 {current_pos}/{self.total_frames})")
                    # 如果在播放状态，停止播放
                    if self.is_playing:
                        self.is_playing = False
                        self.root.after(0, lambda: self.play_button.configure(text="播放"))
                else:
                    print(f"无法读取帧 (当前位置: {current_pos}/{self.total_frames})")
                    # 尝试重新定位到有效帧
                    if self.try_recover_frame_position():
                        return  # 重新尝试显示帧
                return

            # 验证帧数据
            if frame is None:
                print("读取到空帧")
                return

            if frame.size == 0:
                print("读取到空帧数据")
                return

            # 添加字幕到帧
            try:
                if self.subtitle_enabled.get() and self.subtitle_text:
                    frame = self.add_subtitle_to_frame(frame, self.subtitle_text)
            except Exception as subtitle_error:
                print(f"添加字幕时出错: {subtitle_error}")
                # 继续处理原始帧

            # 转换为PIL图像
            try:
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                pil_image = Image.fromarray(frame_rgb)
            except Exception as convert_error:
                print(f"图像转换时出错: {convert_error}")
                return

            # 调整图像大小以适应显示区域
            try:
                display_width = self.video_frame.winfo_width()
                display_height = self.video_frame.winfo_height()

                if display_width > 1 and display_height > 1:
                    # 保持宽高比
                    img_ratio = pil_image.width / pil_image.height
                    display_ratio = display_width / display_height

                    if img_ratio > display_ratio:
                        new_width = display_width
                        new_height = int(display_width / img_ratio)
                    else:
                        new_height = display_height
                        new_width = int(display_height * img_ratio)

                    # 确保尺寸有效
                    new_width = max(1, new_width)
                    new_height = max(1, new_height)

                    pil_image = pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            except Exception as resize_error:
                print(f"图像缩放时出错: {resize_error}")
                # 使用原始尺寸

            # 转换为Tkinter图像
            try:
                photo = ImageTk.PhotoImage(pil_image)
                self.video_label.configure(image=photo, text="")
                self.video_label.image = photo  # 保持引用
            except Exception as tk_error:
                print(f"Tkinter图像转换时出错: {tk_error}")
                return

            # 更新进度条和时间
            try:
                self.progress_var.set(self.current_frame)
                current_time = self.current_frame / self.fps if self.fps > 0 else 0
                total_time = self.total_frames / self.fps if self.fps > 0 else 0
                self.time_label.configure(text=f"{self.format_time(current_time)} / {self.format_time(total_time)}")
            except Exception as ui_error:
                print(f"更新UI时出错: {ui_error}")

        except Exception as e:
            error_msg = str(e)
            error_type = type(e).__name__
            print(f"显示帧时发生错误: {error_msg}")
            print(f"错误类型: {error_type}")

            # 检测各种类型的OpenCV异常，包括FFmpeg相关错误
            opencv_indicators = [
                "OpenCV", "C++", "cv::", "Mat", "VideoCapture",
                "Unknown C++ exception", "assertion failed",
                "Bad argument", "Unsupported format",
                "libavcodec", "pthread_frame", "async_lock",  # FFmpeg相关错误
                "ffmpeg", "avcodec", "avformat"
            ]

            is_opencv_error = any(indicator in error_msg for indicator in opencv_indicators)
            is_opencv_type = "cv2" in error_type.lower() or "opencv" in error_type.lower()

            if is_opencv_error or is_opencv_type:
                # 检查是否是FFmpeg相关错误
                ffmpeg_indicators = ["libavcodec", "pthread_frame", "async_lock", "ffmpeg", "avcodec"]
                is_ffmpeg_error = any(indicator in error_msg.lower() for indicator in ffmpeg_indicators)

                if is_ffmpeg_error:
                    print("🔧 检测到FFmpeg相关异常，启动专门恢复...")
                    self.handle_ffmpeg_exception(error_msg, error_type)
                else:
                    print("🔧 检测到OpenCV相关异常，启动智能恢复...")
                    self.handle_opencv_exception(error_msg, error_type)
            else:
                print("⚠️  非OpenCV异常，记录错误信息")
                self.log_non_opencv_error(error_msg, error_type)

    def reinitialize_video_capture(self):
        """重新初始化视频捕获（用于处理C++异常）"""
        try:
            if not self.video_path:
                print("没有视频路径，无法重新初始化")
                return False

            print("正在重新初始化视频捕获...")

            # 记录当前状态
            current_pos = self.current_frame
            was_playing = self.is_playing

            # 暂停播放
            if was_playing:
                self.is_playing = False

            # 强制释放当前资源
            self.force_release_video_capture()

            # 等待资源完全释放
            time.sleep(0.2)

            # 尝试多种方式重新创建VideoCapture
            success = self.try_multiple_video_capture_methods()

            if success:
                # 恢复到之前的位置
                self.restore_video_position(current_pos)

                # 如果之前在播放，恢复播放状态
                if was_playing:
                    self.is_playing = True

                print("✓ 视频捕获重新初始化成功")
                return True
            else:
                print("❌ 所有重新初始化方法都失败")
                return False

        except Exception as reinit_error:
            print(f"重新初始化时出错: {reinit_error}")
            return False

    def force_release_video_capture(self):
        """强制释放视频捕获资源"""
        try:
            if self.cap:
                print("强制释放VideoCapture资源...")
                try:
                    self.cap.release()
                except:
                    pass

                # 尝试多次释放
                for i in range(3):
                    try:
                        if hasattr(self.cap, 'release'):
                            self.cap.release()
                    except:
                        pass
                    time.sleep(0.05)

                self.cap = None
                print("✓ VideoCapture资源已释放")
        except Exception as e:
            print(f"释放资源时出错: {e}")

    def try_multiple_video_capture_methods(self):
        """尝试多种方法创建VideoCapture"""
        # 针对FFmpeg断言错误的优化方法 - 完全避免FFMPEG
        methods = [
            ("MSMF后端（推荐）", lambda: self.create_msmf_capture()),
            ("DSHOW后端", lambda: self.create_dshow_capture()),
            ("默认方法（非FFMPEG）", lambda: self.create_default_capture()),
            ("单线程FFMPEG（最后选择）", lambda: self.create_single_thread_capture()),
        ]

        for method_name, method_func in methods:
            try:
                print(f"尝试 {method_name}...")
                cap = method_func()

                if cap and cap.isOpened():
                    # 验证能否读取帧
                    ret, test_frame = cap.read()
                    if ret and test_frame is not None:
                        # 重置到开始位置
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        self.cap = cap
                        print(f"✓ {method_name} 成功")
                        return True
                    else:
                        cap.release()
                        print(f"❌ {method_name} 无法读取帧")
                else:
                    if cap:
                        cap.release()
                    print(f"❌ {method_name} 无法打开")

            except Exception as e:
                print(f"❌ {method_name} 失败: {e}")
                continue

        return False

    def create_msmf_capture(self):
        """创建MSMF后端VideoCapture（已禁用 - 有循环播放崩溃bug）"""
        print("⚠️ MSMF后端已被禁用 - 存在循环播放时的崩溃问题")
        print("   错误: cap_msmf.cpp:124 Assertion failed (p == NULL)")
        print("   解决: 使用更稳定的FFMPEG或其他后端")
        print("   详情: https://github.com/opencv/opencv/issues/...")
        return None

    def create_dshow_capture(self):
        """创建DirectShow后端VideoCapture"""
        try:
            print("使用DirectShow后端...")
            cap = cv2.VideoCapture(self.video_path, cv2.CAP_DSHOW)

            if cap.isOpened():
                # 设置DirectShow参数
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                # 测试读取
                ret, test_frame = cap.read()
                if ret and test_frame is not None:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    print("✓ DirectShow后端创建成功")
                    return cap
                else:
                    cap.release()

            print("❌ DirectShow后端无法读取帧")
            return None

        except Exception as e:
            print(f"❌ DirectShow后端创建失败: {e}")
            return None

    def create_default_capture(self):
        """创建默认后端VideoCapture（避免MSMF和FFMPEG）"""
        try:
            print("使用默认后端（避免MSMF）...")

            # 🛡️ 设置安全的后端优先级，完全避免MSMF
            import os
            original_env = os.environ.get('OPENCV_VIDEOIO_PRIORITY_LIST', '')
            os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = 'FFMPEG,DSHOW,V4L2'  # 移除MSMF

            try:
                cap = cv2.VideoCapture(self.video_path)

                if cap.isOpened():
                    # 检查后端类型
                    backend = cap.getBackendName()
                    print(f"默认后端使用: {backend}")

                    # 🛡️ 检查是否意外使用了MSMF后端
                    if 'MSMF' in backend.upper():
                        print("⚠️ 默认后端意外使用了MSMF，强制跳过以避免崩溃")
                        cap.release()
                        return None

                    # 设置参数
                    cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                    # 测试读取
                    ret, test_frame = cap.read()
                    if ret and test_frame is not None:
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        print(f"✓ 默认后端（{backend}）创建成功")
                        return cap
                    else:
                        cap.release()

                print("❌ 默认后端无法读取帧")
                return None

            finally:
                # 恢复环境变量
                if original_env:
                    os.environ['OPENCV_VIDEOIO_PRIORITY_LIST'] = original_env
                else:
                    os.environ.pop('OPENCV_VIDEOIO_PRIORITY_LIST', None)

        except Exception as e:
            print(f"❌ 默认后端创建失败: {e}")
            return None

    def create_single_thread_capture(self):
        """创建单线程VideoCapture以避免FFmpeg多线程问题"""
        try:
            print("使用单线程FFMPEG...")
            # 设置OpenCV环境变量以禁用多线程
            import os

            # 保存原始环境变量
            original_threads = os.environ.get('OPENCV_FFMPEG_CAPTURE_OPTIONS', '')

            # 设置单线程选项
            os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1'

            try:
                # 创建VideoCapture
                cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)

                # 如果成功，进一步设置单线程参数
                if cap.isOpened():
                    # 尝试设置线程数为1
                    try:
                        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 减少缓冲区
                    except:
                        pass

                    # 测试读取
                    ret, test_frame = cap.read()
                    if ret and test_frame is not None:
                        cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置位置
                        print("✓ 单线程FFMPEG创建成功")
                        return cap
                    else:
                        cap.release()
                        print("❌ 单线程FFMPEG无法读取帧")
                        return None
                else:
                    print("❌ 单线程FFMPEG无法打开")
                    return None

            finally:
                # 恢复原始环境变量
                if original_threads:
                    os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = original_threads
                else:
                    os.environ.pop('OPENCV_FFMPEG_CAPTURE_OPTIONS', None)

        except Exception as e:
            print(f"❌ 创建单线程捕获失败: {e}")
            return None

    def create_standard_ffmpeg_capture(self):
        """创建标准FFMPEG VideoCapture（最后选择，有风险）"""
        try:
            print("⚠️  使用标准FFMPEG（有FFmpeg错误风险）...")
            cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)

            if cap.isOpened():
                # 设置基本参数
                cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                # 测试读取
                ret, test_frame = cap.read()
                if ret and test_frame is not None:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置位置
                    print("✓ 标准FFMPEG创建成功（注意：可能出现FFmpeg错误）")

                    # 设置FFmpeg错误监控标志
                    self.ffmpeg_error_risk = True
                    return cap
                else:
                    cap.release()
                    print("❌ 标准FFMPEG无法读取帧")
                    return None
            else:
                print("❌ 标准FFMPEG无法打开")
                return None

        except Exception as e:
            print(f"❌ 标准FFMPEG创建失败: {e}")
            return None

    def diagnose_loading_failure(self, video_path):
        """诊断视频加载失败的原因"""
        print("\n" + "=" * 50)
        print("🔍 视频加载失败诊断")
        print("=" * 50)

        try:
            # 1. 文件基本检查
            print("1. 文件基本信息:")
            if not os.path.exists(video_path):
                print("   ❌ 文件不存在")
                return

            file_size = os.path.getsize(video_path)
            print(f"   文件大小: {file_size:,} 字节 ({file_size / (1024*1024):.2f} MB)")

            if file_size == 0:
                print("   ❌ 文件大小为0，文件可能损坏")
                return

            # 2. 文件格式检查
            print("2. 文件格式检查:")
            _, ext = os.path.splitext(video_path)
            print(f"   扩展名: {ext}")

            # 检查文件头
            try:
                with open(video_path, 'rb') as f:
                    header = f.read(32)
                    print(f"   文件头: {header[:8].hex()}")

                    if header[4:8] == b'ftyp':
                        print("   ✓ 检测到MP4文件签名")
                    else:
                        print("   ⚠️  未检测到标准MP4文件签名")
            except Exception as e:
                print(f"   ❌ 无法读取文件头: {e}")

            # 3. OpenCV版本和后端信息
            print("3. OpenCV信息:")
            print(f"   OpenCV版本: {cv2.__version__}")

            # 获取可用后端
            backends = []
            try:
                # 尝试获取后端信息
                if hasattr(cv2, 'videoio_registry'):
                    backends = cv2.videoio_registry.getBackends()
                print(f"   可用后端: {backends}")
            except:
                print("   无法获取后端信息")

            # 4. 系统信息
            print("4. 系统信息:")
            import platform
            print(f"   操作系统: {platform.system()} {platform.release()}")
            print(f"   Python版本: {platform.python_version()}")

            # 5. 建议的解决方案
            print("5. 建议的解决方案:")
            solutions = [
                "尝试使用其他视频播放器验证文件是否正常",
                "使用ffmpeg转换视频格式: ffmpeg -i input.mp4 -c:v libx264 -c:a aac output.mp4",
                "检查文件是否完整下载",
                "尝试将文件复制到本地磁盘",
                "更新OpenCV到最新版本",
                "安装K-Lite Codec Pack或类似的编解码器包"
            ]

            for i, solution in enumerate(solutions, 1):
                print(f"   {i}. {solution}")

        except Exception as e:
            print(f"诊断过程中出错: {e}")

        print("=" * 50)

    def restore_video_position(self, target_frame):
        """恢复视频位置"""
        try:
            if not self.cap or target_frame <= 0:
                return

            print(f"恢复到帧位置: {target_frame}")

            # 尝试直接跳转
            success = self.cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
            if success:
                # 验证位置
                actual_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                if abs(actual_pos - target_frame) <= 5:  # 允许5帧误差
                    self.current_frame = actual_pos
                    print(f"✓ 成功恢复到帧 {actual_pos}")
                    return

            # 如果直接跳转失败，尝试渐进式跳转
            print("直接跳转失败，尝试渐进式恢复...")
            step_size = max(1, target_frame // 10)  # 分10步跳转
            current = 0

            while current < target_frame and current < self.total_frames:
                next_pos = min(current + step_size, target_frame)
                if self.cap.set(cv2.CAP_PROP_POS_FRAMES, next_pos):
                    current = next_pos
                else:
                    break

            self.current_frame = current
            print(f"✓ 渐进式恢复到帧 {current}")

        except Exception as e:
            print(f"恢复位置时出错: {e}")
            self.current_frame = 0

    def handle_opencv_exception(self, error_msg, error_type):
        """智能处理OpenCV异常"""
        print(f"🔍 分析OpenCV异常: {error_type}")

        # 记录异常历史
        if not hasattr(self, 'opencv_error_count'):
            self.opencv_error_count = 0
        if not hasattr(self, 'last_opencv_error_time'):
            self.last_opencv_error_time = 0

        current_time = time.time()
        self.opencv_error_count += 1

        # 如果短时间内频繁出错，采用更激进的恢复策略
        if current_time - self.last_opencv_error_time < 5.0:  # 5秒内
            print(f"⚠️  短时间内第 {self.opencv_error_count} 次OpenCV异常")
            if self.opencv_error_count >= 3:
                print("🚨 频繁异常，启动深度恢复模式")
                self.deep_recovery_mode()
                return
        else:
            self.opencv_error_count = 1  # 重置计数

        self.last_opencv_error_time = current_time

        # 根据错误类型选择恢复策略
        if "assertion failed" in error_msg.lower():
            print("🔧 检测到断言失败，尝试重置视频参数")
            self.reset_video_parameters()
        elif "bad argument" in error_msg.lower():
            print("🔧 检测到参数错误，尝试参数修正")
            self.fix_video_parameters()
        elif "unsupported format" in error_msg.lower():
            print("🔧 检测到格式不支持，尝试格式转换")
            self.handle_format_issue()
        else:
            print("🔧 通用OpenCV异常，尝试标准恢复")
            self.standard_opencv_recovery()

    def deep_recovery_mode(self):
        """深度恢复模式（用于频繁异常）"""
        print("🚨 启动深度恢复模式...")

        try:
            # 1. 完全停止播放
            self.is_playing = False
            self.is_paused = True

            # 2. 强制释放所有资源
            self.force_release_video_capture()

            # 3. 清理内存
            import gc
            gc.collect()

            # 4. 等待更长时间
            time.sleep(1.0)

            # 5. 重新加载视频
            if self.video_path:
                print("🔄 重新加载视频文件...")
                self.load_video(self.video_path)

            # 6. 重置错误计数
            self.opencv_error_count = 0

            print("✅ 深度恢复完成")

        except Exception as e:
            print(f"❌ 深度恢复失败: {e}")

    def reset_video_parameters(self):
        """重置视频参数"""
        try:
            print("🔧 重置视频参数...")
            if self.cap and self.cap.isOpened():
                # 重置到安全的参数
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                self.current_frame = 0
                print("✅ 视频参数已重置")
            else:
                self.reinitialize_video_capture()
        except Exception as e:
            print(f"❌ 重置参数失败: {e}")
            self.reinitialize_video_capture()

    def fix_video_parameters(self):
        """修正视频参数"""
        try:
            print("🔧 修正视频参数...")
            if self.cap and self.cap.isOpened():
                # 验证当前参数
                current_frame = max(0, min(self.current_frame, self.total_frames - 1))
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, current_frame)
                self.current_frame = current_frame
                print(f"✅ 参数已修正到帧 {current_frame}")
            else:
                self.reinitialize_video_capture()
        except Exception as e:
            print(f"❌ 修正参数失败: {e}")
            self.reinitialize_video_capture()

    def handle_format_issue(self):
        """处理格式问题"""
        print("🔧 处理视频格式问题...")
        print("💡 建议: 尝试使用标准的H.264编码MP4文件")
        # 尝试重新初始化，使用不同的后端
        self.reinitialize_video_capture()

    def standard_opencv_recovery(self):
        """标准OpenCV恢复"""
        print("🔧 执行标准OpenCV恢复...")
        success = self.reinitialize_video_capture()
        if not success:
            print("⚠️  标准恢复失败，尝试深度恢复")
            self.deep_recovery_mode()

    def log_non_opencv_error(self, error_msg, error_type):
        """记录非OpenCV错误"""
        print(f"📝 记录非OpenCV错误: {error_type}")
        print(f"📝 错误详情: {error_msg}")

        # 可以在这里添加错误日志记录
        # 例如写入文件或发送到日志系统

    def handle_ffmpeg_exception(self, error_msg, error_type):
        """专门处理FFmpeg相关异常"""
        print(f"🔍 分析FFmpeg异常: {error_type}")
        print(f"错误详情: {error_msg}")

        # FFmpeg多线程相关错误的特殊处理
        if "pthread_frame" in error_msg or "async_lock" in error_msg:
            print("🚨 检测到FFmpeg多线程锁定问题")
            self.handle_ffmpeg_threading_issue()
        elif "libavcodec" in error_msg:
            print("🚨 检测到FFmpeg编解码器问题")
            self.handle_ffmpeg_codec_issue()
        else:
            print("🚨 通用FFmpeg错误处理")
            self.handle_general_ffmpeg_issue()

    def handle_ffmpeg_threading_issue(self):
        """处理FFmpeg多线程问题"""
        print("🔧 处理FFmpeg多线程问题...")

        try:
            # 1. 完全停止播放
            self.is_playing = False

            # 2. 强制释放所有资源
            self.force_release_video_capture()

            # 3. 等待更长时间让线程完全退出
            time.sleep(1.0)

            # 4. 清理内存
            import gc
            gc.collect()

            # 5. 使用单线程模式重新创建
            print("🔄 使用单线程模式重新创建VideoCapture...")
            new_cap = self.create_single_thread_capture()

            if new_cap and new_cap.isOpened():
                self.cap = new_cap
                self.current_frame = 0
                print("✅ FFmpeg多线程问题已解决")
                return True
            else:
                print("❌ 单线程模式也失败，尝试其他方法")
                return self.try_multiple_video_capture_methods()

        except Exception as e:
            print(f"❌ FFmpeg多线程问题处理失败: {e}")
            return False

    def handle_ffmpeg_codec_issue(self):
        """处理FFmpeg编解码器问题"""
        print("🔧 处理FFmpeg编解码器问题...")

        try:
            # 强制释放资源
            self.force_release_video_capture()
            time.sleep(0.5)

            # 尝试使用不同的后端，避免FFmpeg
            non_ffmpeg_methods = [
                ("MSMF后端", lambda: cv2.VideoCapture(self.video_path, cv2.CAP_MSMF)),
                ("DSHOW后端", lambda: cv2.VideoCapture(self.video_path, cv2.CAP_DSHOW)),
            ]

            for method_name, method_func in non_ffmpeg_methods:
                try:
                    print(f"尝试 {method_name}...")
                    cap = method_func()

                    if cap and cap.isOpened():
                        ret, test_frame = cap.read()
                        if ret and test_frame is not None:
                            cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                            self.cap = cap
                            self.current_frame = 0
                            print(f"✅ {method_name} 成功解决编解码器问题")
                            return True
                        else:
                            cap.release()

                except Exception as e:
                    print(f"❌ {method_name} 失败: {e}")
                    continue

            print("❌ 所有非FFmpeg方法都失败")
            return False

        except Exception as e:
            print(f"❌ FFmpeg编解码器问题处理失败: {e}")
            return False

    def handle_general_ffmpeg_issue(self):
        """处理通用FFmpeg问题"""
        print("🔧 处理通用FFmpeg问题...")

        # 使用深度恢复模式
        self.deep_recovery_mode()
        return True

    def handle_msmf_error_emergency(self):
        """处理MSMF错误的紧急恢复"""
        try:
            print("🚨 启动MSMF错误紧急恢复...")

            # 1. 立即停止播放
            self.is_playing = False

            # 2. 强制释放当前capture
            if self.cap:
                try:
                    self.cap.release()
                    print("✓ 已释放MSMF capture")
                except:
                    print("⚠️ 释放MSMF capture时出错")
                self.cap = None

            # 3. 强制垃圾回收
            import gc
            gc.collect()

            # 4. 等待资源完全释放
            time.sleep(0.5)

            # 5. 使用安全的后端重新创建capture
            print("🔄 使用安全后端重新创建capture...")

            # 优先使用FFMPEG后端
            safe_methods = [
                ("单线程FFMPEG", self.create_single_thread_capture),
                ("标准FFMPEG", self.create_standard_ffmpeg_capture),
                ("DSHOW后端", self.create_dshow_capture),
            ]

            for method_name, method_func in safe_methods:
                try:
                    print(f"尝试 {method_name}...")
                    new_cap = method_func()
                    if new_cap and new_cap.isOpened():
                        # 验证后端不是MSMF
                        try:
                            backend_name = new_cap.getBackendName()
                            if 'MSMF' in backend_name.upper():
                                print(f"⚠️ {method_name} 意外使用了MSMF，跳过")
                                new_cap.release()
                                continue
                        except:
                            pass  # 无法获取后端名称，继续尝试

                        # 恢复到当前播放位置
                        try:
                            new_cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame)
                        except:
                            new_cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                            self.current_frame = 0

                        self.cap = new_cap
                        print(f"✅ {method_name} 恢复成功")

                        # 重新启动播放
                        self.is_playing = True
                        return True

                except Exception as e:
                    print(f"❌ {method_name} 恢复失败: {e}")
                    continue

            print("❌ 所有安全后端都无法恢复")
            return False

        except Exception as e:
            print(f"❌ MSMF紧急恢复失败: {e}")
            return False

    def handle_pthread_error_emergency(self):
        """处理pthread_frame.c错误的紧急恢复"""
        try:
            print("🚨 启动pthread_frame.c错误紧急恢复...")

            # 1. 立即停止播放
            self.is_playing = False

            # 2. 强制释放当前capture
            if self.cap:
                try:
                    self.cap.release()
                    print("✓ 已释放pthread有问题的capture")
                except:
                    print("⚠️ 释放pthread capture时出错")
                self.cap = None

            # 3. 强制垃圾回收
            import gc
            gc.collect()

            # 4. 重新设置pthread安全的环境变量
            os.environ['OPENCV_FFMPEG_CAPTURE_OPTIONS'] = 'threads;1|thread_type;1'
            os.environ['OPENCV_FFMPEG_WRITER_OPTIONS'] = 'threads;1'
            os.environ['OPENCV_FFMPEG_THREAD_COUNT'] = '1'
            os.environ['FFMPEG_THREAD_SAFE'] = '1'
            os.environ['OPENCV_VIDEOIO_FFMPEG_DISABLE_HARDWARE_ACCELERATION'] = '1'

            # 5. 等待更长时间让线程完全释放
            time.sleep(0.8)

            # 6. 使用pthread安全的方式重新创建capture
            print("🔄 使用pthread安全方式重新创建capture...")

            # 只使用单线程FFMPEG
            try:
                new_cap = cv2.VideoCapture(self.video_path, cv2.CAP_FFMPEG)
                if new_cap and new_cap.isOpened():
                    # 设置最安全的参数
                    try:
                        new_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # 最小缓冲区
                        # 不设置任何线程相关参数，避免触发pthread错误
                    except:
                        pass

                    # 恢复到当前播放位置
                    try:
                        new_cap.set(cv2.CAP_PROP_POS_FRAMES, self.current_frame)
                    except:
                        new_cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        self.current_frame = 0

                    self.cap = new_cap
                    print("✅ pthread安全capture恢复成功")

                    # 重新启动播放
                    self.is_playing = True
                    return True
                else:
                    print("❌ pthread安全capture创建失败")
                    return False

            except Exception as e:
                print(f"❌ pthread安全恢复失败: {e}")
                return False

        except Exception as e:
            print(f"❌ pthread紧急恢复失败: {e}")
            return False

    def handle_playback_opencv_exception(self, error_msg, consecutive_errors):
        """处理播放循环中的OpenCV异常"""
        print(f"🔍 分析播放异常 (连续错误: {consecutive_errors})")

        # 根据连续错误次数选择恢复策略
        if consecutive_errors <= 2:
            print("🔧 轻度恢复: 重新初始化VideoCapture")
            return self.reinitialize_video_capture()
        elif consecutive_errors <= 4:
            print("🔧 中度恢复: 重置到安全位置")
            return self.reset_to_safe_position()
        else:
            print("🔧 重度恢复: 深度恢复模式")
            self.deep_recovery_mode()
            return True  # 深度恢复后继续尝试

    def reset_to_safe_position(self):
        """重置到安全位置"""
        try:
            print("🔄 重置到安全位置...")

            # 强制释放资源
            self.force_release_video_capture()
            time.sleep(0.3)

            # 重新创建VideoCapture
            if self.try_multiple_video_capture_methods():
                # 跳转到视频开始或中间的安全位置
                safe_positions = [0, self.total_frames // 4, self.total_frames // 2]

                for pos in safe_positions:
                    try:
                        self.cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
                        ret, test_frame = self.cap.read()
                        if ret and test_frame is not None:
                            self.current_frame = pos
                            print(f"✅ 成功重置到安全位置: 帧 {pos}")
                            return True
                    except:
                        continue

            print("❌ 重置到安全位置失败")
            return False

        except Exception as e:
            print(f"❌ 重置过程出错: {e}")
            return False

    def try_recover_frame_position(self):
        """尝试恢复帧位置（当读取失败时）"""
        try:
            print("尝试恢复帧位置...")

            if not self.cap or not self.cap.isOpened():
                print("VideoCapture对象无效，尝试重新初始化")
                return self.reinitialize_video_capture()

            # 获取当前位置
            current_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))

            # 如果当前位置超出范围，回到开始
            if current_pos >= self.total_frames:
                print(f"位置超出范围 ({current_pos} >= {self.total_frames})，回到开始")
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                self.current_frame = 0
                return True

            # 尝试向前跳几帧
            recovery_positions = [
                max(0, current_pos - 5),  # 向前5帧
                max(0, current_pos - 10), # 向前10帧
                0  # 回到开始
            ]

            for pos in recovery_positions:
                try:
                    print(f"尝试跳转到帧 {pos}")
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
                    ret, test_frame = self.cap.read()

                    if ret and test_frame is not None:
                        print(f"成功恢复到帧 {pos}")
                        self.current_frame = pos
                        # 重新设置位置以便下次读取
                        self.cap.set(cv2.CAP_PROP_POS_FRAMES, pos)
                        return True

                except Exception as seek_error:
                    print(f"跳转到帧 {pos} 失败: {seek_error}")
                    continue

            # 如果所有恢复尝试都失败，重新初始化
            print("所有恢复尝试失败，重新初始化VideoCapture")
            return self.reinitialize_video_capture()

        except Exception as recovery_error:
            print(f"帧位置恢复失败: {recovery_error}")
            return False
            
    def add_subtitle_to_frame(self, frame, text):
        """在帧上添加字幕"""
        if not text.strip():
            return frame
            
        # 转换为PIL图像以便添加文字
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        pil_image = Image.fromarray(frame_rgb)
        draw = ImageDraw.Draw(pil_image)
        
        # 尝试加载字体
        try:
            # Windows系统字体路径
            font_path = "C:/Windows/Fonts/msyh.ttc"  # 微软雅黑
            if not os.path.exists(font_path):
                font_path = "C:/Windows/Fonts/simsun.ttc"  # 宋体
            
            if os.path.exists(font_path):
                font = ImageFont.truetype(font_path, self.subtitle_font_size)
            else:
                font = ImageFont.load_default()
        except:
            font = ImageFont.load_default()
        
        # 计算文字位置
        img_width, img_height = pil_image.size
        
        # 分行处理长文本
        words = text.split()
        lines = []
        current_line = ""
        max_width = img_width * 0.9  # 最大宽度为图像宽度的90%
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            bbox = draw.textbbox((0, 0), test_line, font=font)
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        # 绘制每一行
        line_height = self.subtitle_font_size + 5
        total_text_height = len(lines) * line_height
        start_y = int(img_height * self.subtitle_position_y - total_text_height / 2)
        
        for i, line in enumerate(lines):
            bbox = draw.textbbox((0, 0), line, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (img_width - text_width) // 2
            y = start_y + i * line_height
            
            # 绘制背景
            padding = 5
            bg_bbox = [x - padding, y - padding, 
                      x + text_width + padding, y + text_height + padding]
            draw.rectangle(bg_bbox, fill=self.subtitle_bg_color)
            
            # 绘制文字
            draw.text((x, y), line, font=font, fill=self.subtitle_color)
        
        # 转换回OpenCV格式
        frame_with_subtitle = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        return frame_with_subtitle

    def toggle_play(self):
        """切换播放/暂停状态"""
        if not self.cap:
            messagebox.showwarning("警告", "请先选择视频文件")
            return

        if self.is_playing:
            self.pause_video()
        else:
            self.play_video()

    def play_video(self):
        """开始播放视频"""
        if not self.cap:
            return

        self.is_playing = True
        self.is_paused = False
        self.play_button.configure(text="暂停")

        # 启动视频播放线程
        self.video_thread = threading.Thread(target=self.video_playback_loop, daemon=True)
        self.video_thread.start()

        # 启动字幕功能
        if self.subtitle_enabled.get():
            if self.whisper_model and WHISPER_AVAILABLE:
                # 如果Whisper可用，尝试真实字幕
                print("启动真实字幕生成...")
                self.start_audio_transcription()
            else:
                # 否则显示演示字幕
                print("Whisper不可用，启动演示字幕...")
                self.start_demo_subtitles()

    def pause_video(self):
        """暂停视频"""
        self.is_playing = False
        self.is_paused = True
        self.play_button.configure(text="播放")

        # 停止音频转录
        self.stop_audio_transcription()

    def stop_video(self):
        """停止视频"""
        self.is_playing = False
        self.is_paused = False
        self.play_button.configure(text="播放")

        # 停止音频转录
        self.stop_audio_transcription()

        # 重置到开始位置
        if self.cap:
            self.current_frame = 0
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
            self.show_frame()

    def seek_video(self, value):
        """跳转到指定帧"""
        if not self.cap:
            return

        try:
            frame_number = int(float(value))

            # 边界检查
            if frame_number < 0:
                frame_number = 0
            elif frame_number >= self.total_frames:
                frame_number = max(0, self.total_frames - 1)

            print(f"跳转到帧: {frame_number}/{self.total_frames}")

            # 尝试跳转
            success = self.cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            if not success:
                print(f"跳转到帧 {frame_number} 失败，尝试恢复")
                # 尝试恢复到安全位置
                safe_frame = max(0, min(frame_number, self.total_frames // 2))
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, safe_frame)
                frame_number = safe_frame

            self.current_frame = frame_number

            # 验证跳转是否成功
            actual_pos = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
            if abs(actual_pos - frame_number) > 5:  # 允许5帧的误差
                print(f"跳转位置不准确: 期望 {frame_number}, 实际 {actual_pos}")
                self.current_frame = actual_pos

            if not self.is_playing:
                self.show_frame()

        except Exception as e:
            print(f"视频跳转时出错: {e}")
            # 尝试恢复到开始位置
            try:
                self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                self.current_frame = 0
                if not self.is_playing:
                    self.show_frame()
            except:
                print("无法恢复到开始位置")

    def video_playback_loop(self):
        """视频播放循环"""
        consecutive_errors = 0
        max_consecutive_errors = 5

        while self.is_playing and self.cap:
            try:
                start_time = time.time()

                # 读取并显示帧
                ret, frame = self.cap.read()
                if not ret:
                    # 视频结束
                    print("视频播放结束")
                    self.is_playing = False
                    self.root.after(0, lambda: self.play_button.configure(text="播放"))
                    break

                # 验证帧数据
                if frame is None or frame.size == 0:
                    print("读取到无效帧")
                    consecutive_errors += 1
                    if consecutive_errors >= max_consecutive_errors:
                        print("连续错误过多，停止播放")
                        break
                    continue

                # 重置错误计数
                consecutive_errors = 0

                # 更新当前帧位置
                try:
                    self.current_frame = int(self.cap.get(cv2.CAP_PROP_POS_FRAMES))
                except:
                    # 如果无法获取位置，手动递增
                    self.current_frame += 1

                # 在主线程中更新UI
                self.root.after(0, self.show_frame)

                # 控制播放速度
                elapsed = time.time() - start_time
                sleep_time = max(0, self.frame_delay - elapsed)
                time.sleep(sleep_time)

            except Exception as e:
                error_msg = str(e)
                error_type = type(e).__name__
                print(f"🚨 视频播放循环中出错: {error_msg}")
                print(f"错误类型: {error_type}")
                consecutive_errors += 1

                # 🛡️ 优先检测MSMF相关错误
                msmf_indicators = [
                    "cap_msmf.cpp", "msmf", "MSMF", "ComPtr", "IMFSample",
                    "Assertion failed", "p == NULL"
                ]

                # 🔧 检测pthread_frame.c错误
                pthread_indicators = [
                    "pthread_frame.c", "async_lock", "fctx->async_lock",
                    "libavcodec/pthread_frame.c"
                ]

                is_msmf_error = any(indicator in error_msg for indicator in msmf_indicators)
                is_pthread_error = any(indicator in error_msg for indicator in pthread_indicators)

                if is_pthread_error:
                    print("🚨 检测到pthread_frame.c错误，启动专门恢复...")
                    print("   这是FFmpeg多线程异步锁定冲突")
                    recovery_success = self.handle_pthread_error_emergency()
                    if recovery_success:
                        consecutive_errors = 0  # 重置错误计数
                        time.sleep(0.3)  # 更长等待
                        continue
                    else:
                        print("❌ pthread错误恢复失败，停止播放")
                        break
                elif is_msmf_error:
                    print("🚨 检测到MSMF后端错误，启动紧急恢复...")
                    print("   这是已知的MSMF循环播放bug")
                    recovery_success = self.handle_msmf_error_emergency()
                    if recovery_success:
                        consecutive_errors = 0  # 重置错误计数
                        time.sleep(0.2)  # 稍长等待
                        continue
                    else:
                        print("❌ MSMF错误恢复失败，停止播放")
                        break

                # 智能异常处理
                opencv_indicators = [
                    "OpenCV", "C++", "cv::", "Mat", "VideoCapture",
                    "Unknown C++ exception", "assertion failed",
                    "Bad argument", "Unsupported format"
                ]

                is_opencv_error = any(indicator in error_msg for indicator in opencv_indicators)

                if is_opencv_error:
                    print("🔧 播放循环中检测到OpenCV异常，启动恢复...")
                    recovery_success = self.handle_playback_opencv_exception(error_msg, consecutive_errors)
                    if recovery_success:
                        consecutive_errors = max(0, consecutive_errors - 1)  # 减少错误计数
                        time.sleep(0.1)  # 短暂等待
                        continue

                if consecutive_errors >= max_consecutive_errors:
                    print("连续错误过多，停止播放")
                    self.is_playing = False
                    self.root.after(0, lambda: self.play_button.configure(text="播放"))
                    break

                # 短暂等待后继续
                time.sleep(0.1)

    def start_audio_transcription(self):
        """启动实时音频转录"""
        if not self.whisper_model or not self.video_path:
            print("Whisper模型或视频路径不可用，启动演示字幕")
            self.start_demo_subtitles()
            return

        print("启动实时字幕生成...")
        self.stop_audio_transcription()  # 停止之前的转录

        # 初始化实时字幕系统
        self.init_realtime_subtitle_system()

        # 启动实时字幕线程
        self.transcription_thread = threading.Thread(
            target=self.realtime_subtitle_loop, daemon=True)
        self.transcription_thread.start()

    def stop_audio_transcription(self):
        """停止音频转录"""
        if hasattr(self, 'transcription_thread') and self.transcription_thread:
            # 设置停止标志
            self.transcription_running = False

    def init_realtime_subtitle_system(self):
        """初始化实时字幕系统"""
        self.transcription_running = True
        self.subtitle_segments = []  # 存储已生成的字幕段
        self.current_subtitle_index = 0  # 当前显示的字幕索引
        self.subtitle_generation_complete = False  # 字幕生成是否完成
        self.audio_path = None  # 音频文件路径

        # 字幕缓冲区
        self.subtitle_buffer = queue.Queue()

        # 字幕历史记录，用于避免重复
        self.subtitle_history = []
        self.max_history_size = 10

        print("✓ 实时字幕系统初始化完成")

    def realtime_subtitle_loop(self):
        """实时字幕主循环"""
        try:
            print("🎬 启动实时字幕生成...")

            # 步骤1: 提取音频
            self.root.after(0, lambda: self.update_progress(10, "正在提取音频..."))
            self.audio_path = self.extract_audio_from_video()

            if not self.audio_path:
                print("❌ 音频提取失败，启动演示字幕")
                self.root.after(0, lambda: self.update_progress(0, "音频提取失败"))
                self.start_demo_subtitles()
                return

            print("✓ 音频提取成功，开始实时转录")

            # 步骤2: 启动字幕生成线程
            generation_thread = threading.Thread(
                target=self.subtitle_generation_worker, daemon=True)
            generation_thread.start()

            # 步骤3: 启动字幕显示线程
            display_thread = threading.Thread(
                target=self.subtitle_display_worker, daemon=True)
            display_thread.start()

            # 等待生成完成
            generation_thread.join()

            print("✓ 实时字幕系统运行完成")

        except Exception as e:
            error_msg = f"实时字幕系统错误: {e}"
            print(error_msg)
            self.root.after(0, lambda: self.update_progress(0, "字幕系统错误"))
            # 启动演示字幕作为后备
            self.start_demo_subtitles()

    def subtitle_generation_worker(self):
        """字幕生成工作线程"""
        try:
            print("🔄 开始生成字幕...")
            self.root.after(0, lambda: self.update_progress(20, "正在转录音频..."))

            # 使用Whisper进行转录 - 优化参数防止重复
            print("🔄 开始Whisper转录，使用优化参数...")
            segments, info = self.whisper_model.transcribe(
                self.audio_path,
                beam_size=5,
                language="zh",  # 中文（简体中文优先）
                word_timestamps=True,
                initial_prompt="以下是普通话的句子。",  # 引导模型使用简体中文
                condition_on_previous_text=False,  # 禁用上下文依赖，避免重复
                temperature=0.1,  # 稍微增加随机性，避免卡在重复模式
                compression_ratio_threshold=2.4,  # 优化压缩比
                log_prob_threshold=-1.0,  # 提高质量阈值
                no_speech_threshold=0.6,  # 静音检测阈值
                repetition_penalty=1.1,  # 添加重复惩罚
                length_penalty=1.0,  # 长度惩罚
                patience=1,  # 减少patience避免过度搜索
                suppress_blank=True,  # 抑制空白输出
                suppress_tokens=[-1],  # 抑制特定token
                without_timestamps=False,  # 保留时间戳
                max_initial_timestamp=1.0,  # 限制初始时间戳
                prefix=None,  # 不使用前缀
                vad_filter=True,  # 启用语音活动检测
                vad_parameters=dict(min_silence_duration_ms=500)  # VAD参数
            )

            print(f"✓ Whisper转录完成，检测到语言: {info.language}, 概率: {info.language_probability:.2f}")

            # 处理转录结果并放入缓冲区
            segment_count = 0
            processed_count = 0

            for segment in segments:
                if not self.transcription_running:
                    break

                processed_count += 1

                # 使用安全的字幕处理
                subtitle_data = self.safe_subtitle_processing(segment)

                if subtitle_data:
                    subtitle_data['index'] = segment_count
                    self.subtitle_buffer.put(subtitle_data)
                    segment_count += 1

                    # 更新进度
                    progress = min(20 + (segment_count * 60 / max(processed_count, 10)), 80)
                    self.root.after(0, lambda p=progress, c=segment_count, t=processed_count:
                                  self.update_progress(p, f"已生成 {c}/{t} 段字幕"))
                else:
                    # 跳过无效字幕，但仍更新进度
                    progress = min(20 + (segment_count * 60 / max(processed_count, 10)), 80)
                    self.root.after(0, lambda p=progress, c=segment_count, t=processed_count:
                                  self.update_progress(p, f"处理中 {c}/{t} 段字幕"))

            # 标记生成完成
            self.subtitle_generation_complete = True
            self.subtitle_buffer.put(None)  # 结束标记

            print(f"✓ 字幕生成完成，共 {segment_count} 段")
            self.root.after(0, lambda: self.update_progress(80, f"字幕生成完成 ({segment_count} 段)"))

        except Exception as e:
            error_msg = f"❌ 字幕生成失败: {e}"
            print(error_msg)
            print(f"错误类型: {type(e).__name__}")
            print(f"错误详情: {str(e)}")

            # 尝试恢复
            self.handle_subtitle_generation_error(e)

            self.subtitle_generation_complete = True
            self.subtitle_buffer.put(None)

    def subtitle_display_worker(self):
        """字幕显示工作线程"""
        try:
            print("📺 开始实时显示字幕...")

            while self.transcription_running:
                try:
                    # 从缓冲区获取字幕
                    subtitle_data = self.subtitle_buffer.get(timeout=1.0)

                    if subtitle_data is None:  # 结束标记
                        break

                    # 等待到字幕开始时间
                    self.wait_for_subtitle_time(subtitle_data['start'])

                    if not self.transcription_running:
                        break

                    # 显示字幕
                    self.display_subtitle_realtime(subtitle_data)

                    # 等待到字幕结束时间
                    display_duration = subtitle_data['end'] - subtitle_data['start']
                    time.sleep(min(display_duration, 5.0))  # 最长显示5秒

                except queue.Empty:
                    # 如果生成完成但队列为空，退出
                    if self.subtitle_generation_complete:
                        break
                    continue
                except Exception as e:
                    print(f"⚠️  字幕显示错误: {e}")
                    continue

            print("✓ 字幕显示完成")
            self.root.after(0, lambda: self.update_progress(100, "字幕显示完成"))

        except Exception as e:
            error_msg = f"❌ 字幕显示失败: {e}"
            print(error_msg)
            print(f"错误类型: {type(e).__name__}")

            # 尝试恢复
            self.handle_subtitle_display_error(e)

    def wait_for_subtitle_time(self, start_time):
        """等待到字幕开始时间"""
        while self.transcription_running:
            if not self.is_playing:
                time.sleep(0.1)
                continue

            # 获取当前播放时间
            current_time = self.current_frame / self.fps if self.fps > 0 else 0

            if current_time >= start_time:
                break

            # 等待一小段时间后再检查
            time.sleep(0.1)

    def display_subtitle_realtime(self, subtitle_data):
        """实时显示字幕"""
        try:
            text = subtitle_data['text']
            index = subtitle_data['index']

            def update_ui():
                self.update_subtitle(text)
                progress = 80 + (index * 20 / max(index + 1, 10))
                self.update_progress(progress, f"显示字幕: {text[:20]}...")

            self.root.after(0, update_ui)
            print(f"📝 显示字幕 [{subtitle_data['start']:.1f}s]: {text}")

        except Exception as e:
            print(f"❌ 字幕显示更新失败: {e}")

    def process_subtitle_text(self, text):
        """处理字幕文本，确保简体中文格式"""
        if not text:
            return ""

        try:
            # 基本文本清理
            processed_text = text.strip()

            # 移除多余的空格和换行
            processed_text = ' '.join(processed_text.split())

            # 检测和移除重复内容
            processed_text = self.remove_repetitive_text(processed_text)

            # 标点符号规范化（中文标点）
            # 使用简单的替换方式避免引号问题
            if self.contains_chinese(processed_text):
                processed_text = processed_text.replace(',', '，')
                processed_text = processed_text.replace('.', '。')
                processed_text = processed_text.replace('?', '？')
                processed_text = processed_text.replace('!', '！')
                processed_text = processed_text.replace(':', '：')
                processed_text = processed_text.replace(';', '；')
                processed_text = processed_text.replace('(', '（')
                processed_text = processed_text.replace(')', '）')

            # 移除首尾多余的空格，但保留有意义的标点符号
            processed_text = processed_text.strip()

            # 确保句子长度合理（字幕显示）
            if len(processed_text) > 50:
                # 尝试在合适的位置断句
                processed_text = self.break_long_subtitle(processed_text)

            return processed_text

        except Exception as e:
            print(f"⚠️  字幕文本处理失败: {e}")
            return text  # 返回原始文本

    def contains_chinese(self, text):
        """检查文本是否包含中文字符"""
        import re
        return bool(re.search('[\\u4e00-\\u9fff]', text))

    def remove_repetitive_text(self, text):
        """检测和移除重复的文本内容"""
        if not text or len(text) < 4:
            return text

        try:
            # 首先处理明显的重复模式

            # 1. 检测连续重复的词语
            words = text.split()
            if len(words) >= 2:
                cleaned_words = []
                i = 0
                while i < len(words):
                    word = words[i]
                    # 检查连续重复
                    repeat_count = 1
                    while (i + repeat_count < len(words) and
                           words[i + repeat_count] == word):
                        repeat_count += 1

                    # 最多保留2个重复
                    for _ in range(min(repeat_count, 2)):
                        cleaned_words.append(word)

                    i += repeat_count

                text = ' '.join(cleaned_words)

            # 2. 检测重复的句子模式
            # 按标点符号分割句子
            import re
            sentences = re.split(r'([。！？；])', text)

            # 重新组合句子（包含标点）
            full_sentences = []
            for i in range(0, len(sentences) - 1, 2):
                if i + 1 < len(sentences):
                    sentence = sentences[i].strip() + sentences[i + 1]
                    if sentence.strip():
                        full_sentences.append(sentence.strip())
                elif sentences[i].strip():
                    full_sentences.append(sentences[i].strip())

            # 如果没有找到句子，按原文处理
            if not full_sentences:
                return text

            # 去除重复句子
            unique_sentences = []
            for sentence in full_sentences:
                # 检查是否已存在相同或相似的句子
                is_duplicate = False
                for existing in unique_sentences:
                    # 移除标点后比较
                    clean_sentence = re.sub(r'[。！？；，]', '', sentence)
                    clean_existing = re.sub(r'[。！？；，]', '', existing)

                    if (clean_sentence == clean_existing or
                        self.calculate_similarity(clean_sentence, clean_existing) > 0.9):
                        is_duplicate = True
                        break

                if not is_duplicate:
                    unique_sentences.append(sentence)

            # 重新组合结果
            if unique_sentences:
                result = ''.join(unique_sentences)
                # 确保以适当的标点结尾
                if result and not result.endswith(('。', '！', '？', '；')):
                    result += '。'
                return result
            else:
                return text

        except Exception as e:
            print(f"⚠️  重复文本检测失败: {e}")
            return text

    def split_into_sentences(self, text):
        """将文本分割成句子"""
        import re
        # 按中文标点分割
        sentences = re.split(r'[。！？；]', text)
        return [s.strip() for s in sentences if s.strip()]

    def calculate_similarity(self, text1, text2):
        """计算两个文本的相似度"""
        if not text1 and not text2:
            return 1.0

        if not text1 or not text2:
            return 0.0

        # 清理文本（移除标点和空格）
        import re
        clean1 = re.sub(r'[^\u4e00-\u9fff\w]', '', text1.lower())
        clean2 = re.sub(r'[^\u4e00-\u9fff\w]', '', text2.lower())

        if not clean1 and not clean2:
            return 1.0

        if not clean1 or not clean2:
            return 0.0

        # 使用多种相似度计算方法

        # 1. 字符级Jaccard相似度
        set1 = set(clean1)
        set2 = set(clean2)
        jaccard = len(set1.intersection(set2)) / len(set1.union(set2)) if set1.union(set2) else 0.0

        # 2. 最长公共子序列相似度
        lcs_length = self.longest_common_subsequence(clean1, clean2)
        lcs_similarity = (2 * lcs_length) / (len(clean1) + len(clean2)) if (len(clean1) + len(clean2)) > 0 else 0.0

        # 3. 编辑距离相似度
        edit_distance = self.levenshtein_distance(clean1, clean2)
        max_len = max(len(clean1), len(clean2))
        edit_similarity = 1 - (edit_distance / max_len) if max_len > 0 else 1.0

        # 综合相似度（加权平均）
        similarity = (jaccard * 0.3 + lcs_similarity * 0.4 + edit_similarity * 0.3)

        return similarity

    def longest_common_subsequence(self, text1, text2):
        """计算最长公共子序列长度"""
        m, n = len(text1), len(text2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if text1[i-1] == text2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])

        return dp[m][n]

    def levenshtein_distance(self, text1, text2):
        """计算编辑距离"""
        if len(text1) < len(text2):
            return self.levenshtein_distance(text2, text1)

        if len(text2) == 0:
            return len(text1)

        previous_row = list(range(len(text2) + 1))
        for i, c1 in enumerate(text1):
            current_row = [i + 1]
            for j, c2 in enumerate(text2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row

        return previous_row[-1]

    def break_long_subtitle(self, text):
        """断开过长的字幕"""
        if len(text) <= 50:
            return text

        # 寻找合适的断句点
        break_points = ['，', '。', '！', '？', '；', '：', ' ']

        for i in range(40, min(len(text), 60)):
            if text[i] in break_points:
                return text[:i+1]

        # 如果没有找到合适的断句点，在50字符处截断
        return text[:47] + "..."

    def handle_subtitle_generation_error(self, error):
        """处理字幕生成错误"""
        error_type = type(error).__name__
        error_msg = str(error)

        print(f"🔍 分析字幕生成错误: {error_type}")

        # 根据错误类型提供具体的解决建议
        if "FileNotFoundError" in error_type:
            print("💡 音频文件未找到，可能是提取失败")
            self.root.after(0, lambda: self.update_progress(0, "音频文件未找到，启动演示字幕"))
            self.start_demo_subtitles()

        elif "OutOfMemoryError" in error_type or "memory" in error_msg.lower():
            print("💡 内存不足，尝试使用更小的模型")
            self.root.after(0, lambda: self.update_progress(0, "内存不足，请重试"))

        elif "timeout" in error_msg.lower():
            print("💡 转录超时，可能是音频文件过大")
            self.root.after(0, lambda: self.update_progress(0, "转录超时，启动演示字幕"))
            self.start_demo_subtitles()

        elif "network" in error_msg.lower() or "download" in error_msg.lower():
            print("💡 网络问题，模型下载失败")
            self.root.after(0, lambda: self.update_progress(0, "网络问题，请检查连接"))

        else:
            print("💡 未知错误，启动演示字幕")
            self.root.after(0, lambda: self.update_progress(0, "字幕生成失败，启动演示字幕"))
            self.start_demo_subtitles()

    def handle_subtitle_display_error(self, error):
        """处理字幕显示错误"""
        error_type = type(error).__name__
        error_msg = str(error)

        print(f"🔍 分析字幕显示错误: {error_type}")

        # 根据错误类型提供具体的解决建议
        if "queue" in error_msg.lower():
            print("💡 队列问题，重新初始化缓冲区")
            try:
                # 清空队列
                while not self.subtitle_buffer.empty():
                    self.subtitle_buffer.get_nowait()
            except:
                pass

        elif "thread" in error_msg.lower():
            print("💡 线程同步问题，重启字幕系统")
            self.transcription_running = False
            time.sleep(0.5)
            self.start_audio_transcription()

        elif "encoding" in error_msg.lower() or "unicode" in error_msg.lower():
            print("💡 字符编码问题，检查文本处理")

        else:
            print("💡 显示错误，继续尝试")

    def validate_subtitle_data(self, subtitle_data):
        """验证字幕数据的有效性"""
        try:
            # 检查必需字段
            required_fields = ['text', 'start', 'end', 'index']
            for field in required_fields:
                if field not in subtitle_data:
                    print(f"⚠️  字幕数据缺少字段: {field}")
                    return False

            # 检查文本内容
            if not subtitle_data['text'] or not subtitle_data['text'].strip():
                print("⚠️  字幕文本为空")
                return False

            # 检查是否与历史字幕重复
            current_text = subtitle_data['text'].strip()
            if self.is_duplicate_subtitle(current_text):
                print(f"⚠️  检测到重复字幕，跳过: {current_text[:20]}...")
                return False

            # 检查时间有效性
            start_time = subtitle_data['start']
            end_time = subtitle_data['end']

            if start_time < 0 or end_time < 0:
                print(f"⚠️  字幕时间为负数: start={start_time}, end={end_time}")
                return False

            if start_time >= end_time:
                print(f"⚠️  字幕时间顺序错误: start={start_time}, end={end_time}")
                return False

            # 检查时间长度合理性
            duration = end_time - start_time
            if duration > 30:  # 单条字幕不应超过30秒
                print(f"⚠️  字幕持续时间过长: {duration}秒")
                return False

            # 如果验证通过，添加到历史记录
            self.add_to_subtitle_history(current_text)

            return True

        except Exception as e:
            print(f"⚠️  字幕数据验证失败: {e}")
            return False

    def is_duplicate_subtitle(self, text):
        """检查字幕是否与历史记录重复"""
        if not hasattr(self, 'subtitle_history'):
            self.subtitle_history = []

        for history_text in self.subtitle_history:
            # 计算相似度
            similarity = self.calculate_similarity(text, history_text)
            if similarity > 0.85:  # 85%以上相似度认为是重复
                return True

        return False

    def add_to_subtitle_history(self, text):
        """添加字幕到历史记录"""
        if not hasattr(self, 'subtitle_history'):
            self.subtitle_history = []

        self.subtitle_history.append(text)

        # 保持历史记录大小限制
        if len(self.subtitle_history) > self.max_history_size:
            self.subtitle_history.pop(0)

    def safe_subtitle_processing(self, segment):
        """安全的字幕处理，包含错误恢复"""
        try:
            # 基本数据提取
            raw_text = segment.text.strip() if hasattr(segment, 'text') else ""
            start_time = getattr(segment, 'start', 0.0)
            end_time = getattr(segment, 'end', start_time + 2.0)

            # 文本处理
            processed_text = self.process_subtitle_text(raw_text)

            # 创建字幕数据
            subtitle_data = {
                'text': processed_text,
                'start': start_time,
                'end': end_time,
                'index': getattr(self, 'current_subtitle_index', 0)
            }

            # 验证数据
            if self.validate_subtitle_data(subtitle_data):
                return subtitle_data
            else:
                print(f"⚠️  字幕数据验证失败，跳过: {raw_text[:30]}...")
                return None

        except Exception as e:
            print(f"⚠️  字幕处理失败: {e}")
            return None



    def extract_audio_from_video(self):
        """从视频中提取音频 - 支持多种方法"""
        if not self.video_path:
            print("错误: 没有视频文件路径")
            return None

        try:
            # 创建临时音频文件
            import tempfile
            temp_audio = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
            temp_audio_path = temp_audio.name
            temp_audio.close()

            # 方法1: 尝试使用moviepy
            if self.extract_audio_with_moviepy(temp_audio_path):
                return temp_audio_path

            # 方法2: 尝试使用ffmpeg
            if self.extract_audio_with_ffmpeg(temp_audio_path):
                return temp_audio_path

            # 方法3: 尝试使用opencv + 手动音频提取
            if self.extract_audio_with_opencv(temp_audio_path):
                return temp_audio_path

            # 如果所有方法都失败，清理临时文件并显示帮助信息
            if os.path.exists(temp_audio_path):
                os.remove(temp_audio_path)

            self.show_audio_extraction_help()
            return None

        except Exception as e:
            print(f"提取音频失败: {e}")
            return None

    def extract_audio_with_moviepy(self, output_path):
        """使用moviepy提取音频"""
        try:
            print("尝试使用moviepy提取音频...")
            from moviepy import VideoFileClip

            # 加载视频文件
            video = VideoFileClip(self.video_path)

            # 提取音频
            audio = video.audio
            if audio is None:
                print("视频文件没有音频轨道")
                video.close()
                return False

            # 保存为WAV格式，16kHz采样率
            audio.write_audiofile(
                output_path,
                fps=16000,  # 16kHz采样率，适合Whisper
                nbytes=2,   # 16位
                codec='pcm_s16le',
                logger=None  # 禁用日志输出
            )

            # 清理资源
            audio.close()
            video.close()

            print(f"✓ moviepy音频提取成功: {output_path}")
            return True

        except ImportError:
            print("moviepy未安装")
            print("提示: 可以运行 'pip install moviepy' 来安装moviepy")
            return False
        except Exception as e:
            print(f"moviepy提取失败: {e}")
            return False

    def extract_audio_with_ffmpeg(self, output_path):
        """使用ffmpeg提取音频"""
        try:
            print("尝试使用ffmpeg提取音频...")
            import subprocess

            # 检查ffmpeg是否可用
            try:
                subprocess.run(["ffmpeg", "-version"],
                             capture_output=True, check=True, timeout=5)
            except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
                print("ffmpeg不可用，跳过此方法")
                return False

            # 使用ffmpeg提取音频
            cmd = [
                "ffmpeg",
                "-i", self.video_path,      # 输入视频文件
                "-ar", "16000",             # 采样率16kHz
                "-ac", "1",                 # 单声道
                "-c:a", "pcm_s16le",        # 音频编码
                "-y",                       # 覆盖输出文件
                output_path                 # 输出音频文件
            ]

            # 执行命令
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60  # 60秒超时
            )

            if result.returncode == 0:
                print(f"✓ ffmpeg音频提取成功: {output_path}")
                return True
            else:
                print(f"ffmpeg提取失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print("ffmpeg提取超时")
            return False
        except Exception as e:
            print(f"ffmpeg提取失败: {e}")
            return False

    def extract_audio_with_opencv(self, output_path):
        """使用OpenCV尝试提取音频（备用方法）"""
        try:
            print("尝试使用OpenCV方法...")

            # 注意：OpenCV本身不支持音频提取
            # 这里我们提供一个替代方案：生成静音音频用于测试
            print("警告: OpenCV不支持音频提取，生成测试用静音音频")

            # 生成3秒的静音音频用于测试
            import wave
            import numpy as np

            sample_rate = 16000
            duration = 3  # 3秒
            samples = int(sample_rate * duration)

            # 生成静音数据
            audio_data = np.zeros(samples, dtype=np.int16)

            # 保存为WAV文件
            with wave.open(output_path, 'w') as wav_file:
                wav_file.setnchannels(1)  # 单声道
                wav_file.setsampwidth(2)  # 16位
                wav_file.setframerate(sample_rate)
                wav_file.writeframes(audio_data.tobytes())

            print(f"✓ 生成测试音频文件: {output_path}")
            print("注意: 这是静音测试文件，不包含实际音频内容")
            return True

        except Exception as e:
            print(f"OpenCV方法失败: {e}")
            return False

    def show_audio_extraction_help(self):
        """显示音频提取帮助信息"""
        help_message = """
音频提取失败！

为了启用实时字幕功能，需要安装音频处理工具：

方法1 - 安装moviepy（推荐）：
pip install moviepy

方法2 - 安装ffmpeg：
1. 下载ffmpeg: https://ffmpeg.org/download.html
2. 将ffmpeg.exe添加到系统PATH环境变量

方法3 - 使用在线工具：
可以先用其他工具将MP4转换为音频文件，然后使用Whisper处理

当前播放器将继续运行，但字幕功能将显示演示内容。
        """

        print(help_message)

        # 在UI中显示帮助信息
        try:
            from tkinter import messagebox
            messagebox.showinfo(
                "音频提取失败",
                "音频提取失败！\n\n"
                "为了启用实时字幕功能，请安装以下工具之一：\n\n"
                "• moviepy: pip install moviepy\n"
                "• ffmpeg: 下载并添加到PATH\n\n"
                "当前将显示演示字幕。"
            )
        except:
            pass  # 如果无法显示对话框，只在控制台显示

    def start_demo_subtitles(self):
        """启动演示字幕（当音频提取失败时使用）"""
        demo_subtitles = [
            "音频提取失败，显示演示字幕",
            "请安装moviepy或ffmpeg以启用真实字幕",
            "pip install moviepy",
            "这是字幕位置和样式的演示",
            "您可以调整字幕大小和位置",
            "支持多种字幕颜色选择",
            "faster-whisper引擎可生成真实字幕",
            "当前显示的是演示效果"
        ]

        def demo_loop():
            subtitle_index = 0
            total_subtitles = len(demo_subtitles)

            while self.is_playing and self.subtitle_enabled.get():
                if subtitle_index < total_subtitles:
                    subtitle_text = demo_subtitles[subtitle_index]

                    # 更新进度
                    progress = ((subtitle_index + 1) / total_subtitles) * 100
                    status = f"演示字幕 ({subtitle_index + 1}/{total_subtitles})"

                    def update_ui(text=subtitle_text, p=progress, s=status):
                        self.update_subtitle(text)
                        self.update_progress(p, s)

                    self.root.after(0, update_ui)
                    subtitle_index = (subtitle_index + 1) % total_subtitles

                time.sleep(3)  # 每3秒更换一次字幕

        if not hasattr(self, 'demo_subtitle_thread') or not self.demo_subtitle_thread.is_alive():
            self.demo_subtitle_thread = threading.Thread(target=demo_loop, daemon=True)
            self.demo_subtitle_thread.start()



    def update_progress(self, progress, status_text):
        """更新字幕生成进度"""
        self.subtitle_progress_var.set(progress)
        self.progress_text_var.set(status_text)

    def update_subtitle(self, text):
        """更新字幕文本"""
        self.subtitle_text = text

    def update_subtitle_font_size(self, value):
        """更新字幕字体大小"""
        self.subtitle_font_size = int(float(value))

    def update_subtitle_position(self, value):
        """更新字幕位置"""
        self.subtitle_position_y = float(value)

    def format_time(self, seconds):
        """格式化时间显示"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"

    def on_closing(self):
        """程序关闭时的清理工作"""
        try:
            print("📝 程序正在安全关闭...")

            # 停止所有线程活动
            self.transcription_running = False

            # 停止视频播放
            self.stop_video()

            # 释放视频资源
            if self.cap:
                try:
                    self.cap.release()
                except:
                    pass
                self.cap = None

            # 等待线程结束
            if hasattr(self, 'audio_thread') and self.audio_thread and self.audio_thread.is_alive():
                try:
                    self.audio_thread.join(timeout=1.0)
                except:
                    pass

            if hasattr(self, 'transcription_thread') and self.transcription_thread and self.transcription_thread.is_alive():
                try:
                    self.transcription_thread.join(timeout=1.0)
                except:
                    pass

            print("✅ 清理完成")

        except Exception as e:
            print(f"⚠️  关闭时出错: {e}")
        finally:
            try:
                self.root.destroy()
            except:
                pass


def main():
    """主函数"""
    import sys
    import traceback

    try:
        print("🚀 启动MP4播放器...")

        root = tk.Tk()
        root.title("MP4播放器 - 带实时字幕")

        # 设置窗口关闭时不立即退出，而是调用清理函数
        def safe_on_closing():
            try:
                print("📝 程序正在安全关闭...")
                app.on_closing()
            except Exception as e:
                print(f"⚠️  关闭时出错: {e}")
                root.destroy()

        print("🔧 初始化播放器组件...")
        app = MP4PlayerWithSubtitles(root)

        # 设置关闭事件
        root.protocol("WM_DELETE_WINDOW", safe_on_closing)

        # 检查命令行参数
        if len(sys.argv) > 1:
            video_file = sys.argv[1]
            print(f"📁 从命令行加载视频: {video_file}")

            # 检查文件是否存在
            if os.path.exists(video_file):
                try:
                    app.load_video(video_file)
                    print("✅ 视频加载成功")
                except Exception as e:
                    print(f"❌ 加载视频失败: {e}")
                    traceback.print_exc()
            else:
                print(f"❌ 文件不存在: {video_file}")
        else:
            print("💡 提示: 可以通过命令行参数指定视频文件")
            print("   例如: python mp4_player_with_subtitles.py video.mp4")

        print("🎬 启动主界面...")

        # 添加异常处理的主循环
        try:
            print("💡 提示: 关闭窗口或按Ctrl+C退出程序")
            root.mainloop()
        except KeyboardInterrupt:
            print("\n⚠️  用户中断程序")
        except Exception as e:
            print(f"❌ 主循环异常: {e}")
            traceback.print_exc()

            # 询问是否重新启动
            try:
                choice = input("\n程序遇到错误，是否重新启动？(y/n): ").lower().strip()
                if choice in ['y', 'yes', '是']:
                    print("🔄 重新启动程序...")
                    main()  # 递归重启
            except:
                pass
        finally:
            print("🔚 程序结束")

    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        traceback.print_exc()
        input("按回车键退出...")  # 防止窗口立即关闭


if __name__ == "__main__":
    main()
